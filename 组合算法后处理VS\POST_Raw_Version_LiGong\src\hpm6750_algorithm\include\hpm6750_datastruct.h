#ifndef _HPM6750_DATASTRUCT_H_
#define _HPM6750_DATASTRUCT_H_

/*
 * HPM6750算法移植 - 数据结构定义文件
 * 移植自HPM6750项目的DATASTRUCT.h
 */

#include "hpm6750_types.h"

// 前向声明
typedef struct _SysVar SysVar;
typedef struct _InitBind InitBind;
typedef struct _Navi Navi;
typedef struct _<PERSON><PERSON>;
typedef struct _GNSSData GNSSData;
typedef struct _InertialSysAlign InertialSysAlign;

// 指针类型定义
typedef SysVar* p_SysVar;
typedef InitBind* p_InitBind;
typedef Navi* p_Navi;
typedef <PERSON><PERSON>* p_<PERSON>;
typedef GNSSData* p_GNSSData;
typedef InertialSysAlign* p_InertialSysAlign;

// 系统变量结构体
typedef struct _SysVar {
    MODE WorkPhase;                    // 系统工作阶段
    TIME Time_SysInit;                 // 系统初始化时间
    TIME Time_CoarseAlign;             // 粗对准时间
    TIME Time_FineAlign;               // 精对准时间
    TIME Time_INS_Alone;               // 纯惯导时间
    TIME Time_Integrated_Navi;         // 组合导航时间
    
    unsigned char isGNSSValid;         // GNSS有效标志
    unsigned char isDampedOK;          // 阻尼完成标志
    unsigned char IsVehicleConstraints; // 载体约束标志
    
    IPARA Navi_Count;                  // 导航计数
    IPARA Filter_Count;                // 滤波计数
} SysVar;

// 初始装订结构体
typedef struct _InitBind {
    LATI r_InitLati;                   // 初始纬度
    LOGI r_InitLogi;                   // 初始经度
    HEIGHT InitHeight;                 // 初始高度
    VEL InitVn[3];                     // 初始速度
    unsigned char isHeadBind;          // 航向装订标志
    ATTI r_InitHead;                   // 初始航向
} InitBind;

// 导航结构体
typedef struct _Navi {
    // 位置信息
    LATI r_Lati;                       // 纬度
    LOGI r_Logi;                       // 经度
    HEIGHT Height;                     // 高度
    
    // 速度信息
    VEL Vn[3];                         // 导航系速度
    VEL LastVn[3];                     // 上一时刻速度
    
    // 姿态信息
    ATTI r_Atti[3];                    // 姿态角(俯仰、横滚、航向)
    MATR Cnb[9];                       // 姿态矩阵
    QUAT Q[4];                         // 姿态四元数
    
    // 角速率信息
    ANGRATE r_Wibb[2][3];              // 陀螺输出
    ANGRATE r_Wien[3];                 // 地球自转角速率
    ANGRATE r_Wenn[3];                 // 载体相对地球转动角速率
    ANGRATE r_Wnbb[2][3];              // 载体相对导航系角速率
    ANGRATE r_AttiRate[3];             // 姿态角速率
    
    // 加速度信息
    ACCELER Fibb[2][3];                // 加速度计输出
    VEL Vibn[3];                       // 导航系比力积分
    
    // 地球参数
    LEN Rm, Rn;                        // 子午圈、卯酉圈半径
    DPARA invRm, invRn;                // 半径倒数
    ACCELER Gn;                        // 重力加速度
    
    // 位移信息
    LEN Sn[3];                         // 导航系位移
    
    // 角度表示
    ATTI d_Atti[3];                    // 角度制姿态角
    LATI d_Lati;                       // 角度制纬度
    LOGI d_Logi;                       // 角度制经度
    ANGRATE d_AttiRate[3];             // 角度制姿态角速率
    
    // 航向相关
    ATTI d_Heading;                    // 航向角
    DPARA d_HeadingRateBuffer[HEADINGRATE_BUFFER_SIZE]; // 航向角速率缓冲
    IPARA HeadingRate_Circle_Count;    // 循环计数
    DPARA d_HeadingRate_Mean;          // 平均航向角速率
    unsigned char isHeadingchange;     // 航向变化标志
    
    // 误差修正
    IPARA Correct_Count;               // 修正计数
    IPARA Navi_Count;                  // 导航计数
    
    // 中间变量
    DELANG r_DelSenbb[3];              // 角增量
    DELANG r_DelSenbb_1[3];            // 前一时刻角增量
    DELANG r_DelSenbb_2[3];            // 前两时刻角增量

    // 比力积分
    VEL Vibn[3];                       // 导航系比力积分

    // 载体系相关
    MATR Cbn[9];                       // 载体系到导航系转换矩阵
    ACCELER Fibn[3];                   // 导航系比力
} Navi;

// GNSS数据结构体
typedef struct _GNSSData {
    LATI Lati;                         // GNSS纬度
    LOGI Logi;                         // GNSS经度
    HEIGHT Height;                     // GNSS高度
    VEL Vn[3];                         // GNSS速度
    ATTI Heading;                      // GNSS航向
    
    unsigned char isValid;             // 数据有效标志
    unsigned char Position_Status;     // 定位状态
    unsigned char rtkStatus;           // RTK状态
    unsigned char Sate_Num;            // 卫星数量
    
    TIME GnssTime;                     // GNSS时间
    DPARA HDOP;                        // 水平精度因子
} GNSSData;

// 卡尔曼滤波结构体
typedef struct _Kalman {
    VEC Xk[DIM_STATE];                 // 状态向量
    VEC Xkk_1[DIM_STATE];              // 状态预测向量
    MATR Pk[DIM_STATE * DIM_STATE];    // 协方差矩阵
    MATR Pkk_1[DIM_STATE * DIM_STATE]; // 协方差预测矩阵
    MATR Fn[DIM_STATE * DIM_STATE];    // 状态转移矩阵
    MATR Fk[DIM_STATE * DIM_STATE];    // 离散状态转移矩阵
    MATR Qk[DIM_STATE * DIM_STATE];    // 过程噪声矩阵
    MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV]; // 观测噪声矩阵
    MATR Hk[DIM_MAX_OBV * DIM_STATE];  // 观测矩阵
    MATR Kk[DIM_STATE * DIM_MAX_OBV];  // 卡尔曼增益矩阵
    VEC Zk[DIM_MAX_OBV];               // 观测向量

    unsigned char isKalmanStart;       // 滤波启动标志
    unsigned char isCorrectError;      // 误差修正标志
    unsigned char isInsRecord;         // 惯导记录标志
    MODE Work_Mode;                    // 工作模式
    MODE Obv_Fit_Mode;                 // 观测拟合模式
    IPARA State;                       // 状态
    IPARA Filter_Count;                // 滤波计数
    IPARA Kal_Count;                   // 卡尔曼计数
} Kalman;

// 惯性系对准结构体
typedef struct _InertialSysAlign {
    LATI r_Lati;                       // 纬度
    LOGI r_Logi;                       // 经度
    HEIGHT Height;                     // 高度
    TIME AlignTime;                    // 对准时间

    MATR AlignCnb[9];                  // 对准姿态矩阵
    QUAT AlignQ[4];                    // 对准四元数

    // 中间计算变量
    MATR Cie[9];                       // 地球系到惯性系转换矩阵
    MATR Cen[9];                       // 地球系到导航系转换矩阵
    MATR Cib0b[9];                     // 惯性系到载体系转换矩阵
    MATR Cbib0[9];                     // 载体系到惯性系转换矩阵

    VEL Vi[3];                         // 惯性系速度
    VEL Vi_T1[3];                      // T1时刻惯性系速度
    VEL Vib0[3];                       // 载体系速度
    VEL Vib0_T1[3];                    // T1时刻载体系速度
    ACCELER Gn;                        // 重力加速度
    ACCELER Fibb[3];                   // 加速度计输出

    ANGRATE r_Wibb[2][3];              // 陀螺输出
    DELANG r_DelSenbb[3];              // 角增量
    DELANG r_DelSenbb_1[3];            // 前一时刻角增量
    DELANG r_DelSenbb_2[3];            // 前两时刻角增量
    QUAT Qbib0[4];                     // 四元数

    TIME T1;                           // 中间时间点
    unsigned char isT1Record;          // T1时刻记录标志
    IPARA AlignCount;                  // 对准计数
} InertialSysAlign;

#endif // _HPM6750_DATASTRUCT_H_
