/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_math.c - HPM6750数学运算函数移植版本                                                                                        */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：从HPM6750项目移植数学运算相关函数                                                                                                */
/*******************************************************************************************************************************************/

#include "../include/hpm6750_types.h"
#include "../include/hpm6750_functions.h"

/*****************************函数说明*************************************/
/*函数名称：Mat_Mul                                                       */
/*函数功能描述：矩阵乘法                                                   */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：矩阵A、矩阵B、维数m、n、p                                     */
/*输出变量：矩阵C = A * B                                                 */
/*返回值：无                                                              */
/**************************************************************************/
void Mat_Mul(MATR const A[], MATR const B[], MATR C[], IPARA m, IPARA n, IPARA p)
{
    IPARA i, j, k;
    for (i = 0; i < m; i++)
    {
        for (j = 0; j < p; j++)
        {
            C[i * p + j] = 0.0;
            for (k = 0; k < n; k++)
            {
                C[i * p + j] += A[i * n + k] * B[k * p + j];
            }
        }
    }
}

/*****************************函数说明*************************************/
/*函数名称：Vec_Cross                                                     */
/*函数功能描述：三维向量叉积                                               */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：向量A、向量B                                                   */
/*输出变量：向量C = A × B                                                 */
/*返回值：无                                                              */
/**************************************************************************/
void Vec_Cross(VEC const A[3], VEC const B[3], VEC C[3])
{
    C[0] = A[1] * B[2] - A[2] * B[1];
    C[1] = A[2] * B[0] - A[0] * B[2];
    C[2] = A[0] * B[1] - A[1] * B[0];
}

/*****************************函数说明*************************************/
/*函数名称：Qua_Mul                                                       */
/*函数功能描述：四元数乘法                                                 */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：四元数Q1、四元数Q2                                            */
/*输出变量：四元数Q = Q1 * Q2                                             */
/*返回值：无                                                              */
/**************************************************************************/
void Qua_Mul(QUAT const Q1[4], QUAT const Q2[4], QUAT Q[4])
{
    Q[0] = Q1[0] * Q2[0] - Q1[1] * Q2[1] - Q1[2] * Q2[2] - Q1[3] * Q2[3];
    Q[1] = Q1[0] * Q2[1] + Q1[1] * Q2[0] + Q1[2] * Q2[3] - Q1[3] * Q2[2];
    Q[2] = Q1[0] * Q2[2] - Q1[1] * Q2[3] + Q1[2] * Q2[0] + Q1[3] * Q2[1];
    Q[3] = Q1[0] * Q2[3] + Q1[1] * Q2[2] - Q1[2] * Q2[1] + Q1[3] * Q2[0];
}

/*****************************函数说明*************************************/
/*函数名称：ComputeDeg_Ex                                                 */
/*函数功能描述：计算角度表示的导航参数                                     */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：导航结构体指针                                                 */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeDeg_Ex(p_Navi lp_Navi)
{
    register IPARA i;
    
    // 角度制姿态角
    for (i = 0; i < 3; i++)
    {
        lp_Navi->d_Atti[i] = lp_Navi->r_Atti[i] * R2D;
    }
    
    // 角度制位置
    lp_Navi->d_Lati = lp_Navi->r_Lati * R2D;
    lp_Navi->d_Logi = lp_Navi->r_Logi * R2D;
    
    // 角度制姿态角速率
    for (i = 0; i < 3; i++)
    {
        lp_Navi->d_AttiRate[i] = lp_Navi->r_AttiRate[i] * R2D;
    }
    
    // 航向角
    lp_Navi->d_Heading = lp_Navi->d_Atti[0];
}

/*****************************函数说明*************************************/
/*函数名称：TransHeading0to360                                            */
/*函数功能描述：将航向角转换到0-360度范围                                  */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：导航结构体指针                                                 */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void TransHeading0to360(p_Navi lp_Navi)
{
    // 将航向角转换到0-360度范围
    while (lp_Navi->d_Heading < 0.0)
    {
        lp_Navi->d_Heading += 360.0;
    }
    while (lp_Navi->d_Heading >= 360.0)
    {
        lp_Navi->d_Heading -= 360.0;
    }
}

/*****************************函数说明*************************************/
/*函数名称：ComputeVnConstraints                                          */
/*函数功能描述：载体约束处理                                               */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：导航结构体指针                                                 */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeVnConstraints(p_Navi lp_Navi)
{
    // 简单的载体约束实现 - 可根据具体需求修改
    // 这里暂时不做处理，保持原有速度
    (void)lp_Navi; // 避免未使用参数警告
}
