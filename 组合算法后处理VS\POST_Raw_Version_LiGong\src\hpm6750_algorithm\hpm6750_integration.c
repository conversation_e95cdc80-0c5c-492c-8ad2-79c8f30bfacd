/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_integration.c - HPM6750算法集成模块                                                                                     */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：用于将HPM6750算法集成到组合算法后处理VS项目的主程序中                                                                         */
/*******************************************************************************************************************************************/

#include "include/hpm6750_types.h"
#include "include/hpm6750_datastruct.h"
#include "include/hpm6750_functions.h"
#include "../nav_includes.h"

// HPM6750算法状态标志
static unsigned char g_HPM6750_Initialized = NO;

/*****************************函数说明*************************************/
/*函数名称：NAV_function_HPM6750                                          */
/*函数功能描述：使用HPM6750算法的导航处理函数                             */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：无                                                            */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void NAV_function_HPM6750(void)
{
    extern unsigned long int g_NavIndex;
    extern _NAV_Data_Full_t NAV_Data_Full;
    extern _NAV_Data_Out_t NAV_Data_Out;
    
    g_NavIndex++;
    
    // 获取传感器数据和系统参数
    Get_Param_Data(&NAV_Data_Full, &combineData);
    Get_IMU_Data(&NAV_Data_Full, &combineData);
    Get_GNSS_Data(&NAV_Data_Full, &combineData);
    Get_ODS_Data(&NAV_Data_Full, &combineData);
    
    // HPM6750算法初始化
    if (g_HPM6750_Initialized == NO)
    {
        HPM6750_Algorithm_Init();
        g_HPM6750_Initialized = YES;
    }
    
    switch(NAV_Data_Full.Nav_Status)
    {
        case E_NAV_STATUS_START: // 开始状态 加载参数配置
        {
            // 参数配置初始化
            Param_Data_Init(&NAV_Data_Full);
            // 加载标定数据
            Load_Standard_Data(&NAV_Data_Full, &combineData);
            SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
        }
        break;
        
        case E_NAV_STATUS_SINS_KF_INITIAL: // SINS KF初始化
        {
            // 使用HPM6750算法进行初始化
            HPM6750_Algorithm_Process(&NAV_Data_Full.IMU, &NAV_Data_Full.GPS, &NAV_Data_Full.SINS);
            
            // 检查初始化是否完成
            if (g_SysVar.WorkPhase >= PHASE_INS_NAVI)
            {
                NAV_Data_Full.SINS.Init_flag = 1;
                SetNavStatus(E_NAV_STATUS_IN_NAV);
            }
        }
        break;
        
        case E_NAV_STATUS_IN_NAV: // 导航运行
        {
            // 使用HPM6750算法进行导航解算
            HPM6750_Algorithm_Process(&NAV_Data_Full.IMU, &NAV_Data_Full.GPS, &NAV_Data_Full.SINS);
            
            // 更新导航状态信息
            Update_NAV_Status_From_HPM6750(&NAV_Data_Full);
        }
        break;
        
        case E_NAV_STATUS_STOP: // 停止导航
        {
            // 停止导航处理
        }
        break;
        
        default: // 其他异常状态
        {
            break;	
        }
    }
    
#ifdef WIN32	
    // 打印当前单元的各种状态迁移Nav_Status,Nav_Standard_flag
    PrintOutStateChange(g_NavIndex, &NAV_Data_Full);
#endif
    
    // 输出数据更新
    Out_Data_Up(&NAV_Data_Out);
}

/*****************************函数说明*************************************/
/*函数名称：Update_NAV_Status_From_HPM6750                                */
/*函数功能描述：从HPM6750算法状态更新导航状态                             */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：导航数据结构体指针                                            */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void Update_NAV_Status_From_HPM6750(_NAV_Data_Full_t* pNavData)
{
    // 根据HPM6750算法的工作阶段更新导航状态
    switch(g_SysVar.WorkPhase)
    {
        case PHASE_SYS_INIT:
        case PHASE_STANDBY:
            pNavData->Nav_Status = E_NAV_STATUS_SINS_KF_INITIAL;
            break;
            
        case PHASE_COARSE_ALIGN:
        case PHASE_FINE_ALIGN:
            pNavData->Nav_Status = E_NAV_STATUS_SINS_KF_INITIAL;
            pNavData->SINS.Align_flag = 1; // 对准中
            break;
            
        case PHASE_INS_NAVI:
            pNavData->Nav_Status = E_NAV_STATUS_IN_NAV;
            pNavData->SINS.Align_flag = 2; // 对准完成，纯惯导
            pNavData->SINS.KF_flag = 0;    // 卡尔曼滤波未启动
            break;
            
        case PHASE_INTEGRATED_NAVI:
            pNavData->Nav_Status = E_NAV_STATUS_IN_NAV;
            pNavData->SINS.Align_flag = 2; // 对准完成
            pNavData->SINS.KF_flag = 1;    // 卡尔曼滤波运行
            break;
            
        case PHASE_FAIL:
            pNavData->Nav_Status = E_NAV_STATUS_STOP;
            break;
            
        default:
            break;
    }
    
    // 更新GNSS有效性标志
    pNavData->GPS.gps_up_flag = g_SysVar.isGNSSValid;
    
    // 更新导航质量指标
    if (g_SysVar.WorkPhase == PHASE_INTEGRATED_NAVI)
    {
        pNavData->SINS.Nav_Standard_flag = 1; // 组合导航精度
    }
    else if (g_SysVar.WorkPhase == PHASE_INS_NAVI)
    {
        pNavData->SINS.Nav_Standard_flag = 2; // 纯惯导精度
    }
    else
    {
        pNavData->SINS.Nav_Standard_flag = 0; // 低精度或无效
    }
}

/*****************************函数说明*************************************/
/*函数名称：Get_HPM6750_Algorithm_Status                                  */
/*函数功能描述：获取HPM6750算法运行状态信息                               */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：无                                                            */
/*输出变量：状态信息字符串                                                */
/*返回值：无                                                              */
/**************************************************************************/
void Get_HPM6750_Algorithm_Status(char* status_str, int max_len)
{
    snprintf(status_str, max_len, 
        "HPM6750 Status: Phase=%d, GNSS=%s, NavCount=%d, FilterCount=%d, AlignTime=%.1f",
        g_SysVar.WorkPhase,
        (g_SysVar.isGNSSValid == YES) ? "Valid" : "Invalid",
        g_Navi.Navi_Count,
        g_Kalman.Filter_Count,
        g_InertialSysAlign.AlignTime);
}
