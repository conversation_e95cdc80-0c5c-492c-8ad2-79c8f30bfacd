/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_kalman.c - HPM6750卡尔曼滤波算法移植版本                                                                                    */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：从HPM6750项目INAV/kalman.c移植而来，适配组合算法后处理VS项目                                                                     */
/*******************************************************************************************************************************************/

#include "../include/hpm6750_types.h"
#include "../include/hpm6750_datastruct.h"
#include "../include/hpm6750_functions.h"

// 全局变量声明 (移植时需要适配)
extern SysVar g_SysVar;
extern InitBind g_InitBind;
extern Navi g_Navi;
extern Kalman g_Kalman;
extern GNSSData g_GNSSData_In_Use;
extern InertialSysAlign g_InertialSysAlign;

/*********************************函数说明***************************************/
/*函数名称：Kalman_Init                                                          */
/*函数功能描述：完成Kalman滤波结构体的初始化                                     */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：指针:Kalman滤波结构体lp_Kalman                                       */
/*输出变量：指针:Kalman滤波结构体lp_Kalman                                       */
/*备注1：请在Kalman滤波解算开始前调用该函数进行Kalman滤波结构体的初始化          */
/*备注2：该函数为各单个矩阵初始化函数的封装函数，输入输出变量lp_Kalman为地址传递 */
/*备注3：该函数调用了C语言标准库函数memset                                       */
/*返回值：无                                                                     */
/*********************************************************************************/
void Kalman_Init(p_Kalman lp_Kalman)
{
    memset(lp_Kalman, 0, sizeof(Kalman));   //将Kalman结构体成员变量全部清零
    
    // 初始化状态向量
    memset(lp_Kalman->Xk, 0, sizeof(lp_Kalman->Xk));
    
    // 初始化协方差矩阵
    Pk_Init(lp_Kalman->Pk);
    
    // 初始化过程噪声矩阵
    Qk_Init(lp_Kalman->Qk);
    
    // 设置初始状态
    lp_Kalman->isKalmanStart = NO;
    lp_Kalman->isCorrectError = NO;
    lp_Kalman->Work_Mode = 0;
    lp_Kalman->State = 0;
    lp_Kalman->Filter_Count = 0;
}

/*********************************函数说明***************************************/
/*函数名称：KalCompute                                                           */
/*函数功能描述：完成一次Kalman滤波解算                                           */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：指针:GNSS数据结构体lp_GNSSData                                       */
/*输入输出变量：指针:Kalman滤波结构体lp_Kalman                                   */
/*备注：该函数输入变量、输出变量均为地址传递                                     */
/*返回值：无                                                                     */
/*********************************************************************************/
void KalCompute(p_GNSSData lp_GNSSData, p_Kalman lp_Kalman)
{
    if(lp_Kalman->isKalmanStart == YES)
    {
        switch(lp_Kalman->Work_Mode)
        {
            case 1: // PREDICT模式
                KalPredict(lp_Kalman);
                break;
                
            case 2: // FILTER模式
                KalPredict(lp_Kalman);
                KalUpdate(lp_GNSSData, lp_Kalman);
                break;
                
            default:
                break;
        }
        
        lp_Kalman->isKalmanStart = NO;
        lp_Kalman->Filter_Count++;
    }
}

/*********************************函数说明***************************************/
/*函数名称：KalPredict                                                           */
/*函数功能描述：Kalman滤波预测步骤                                               */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入输出变量：指针:Kalman滤波结构体lp_Kalman                                   */
/*返回值：无                                                                     */
/*********************************************************************************/
void KalPredict(p_Kalman lp_Kalman)
{
    // 计算状态转移矩阵
    ComputeFn(&g_Navi, lp_Kalman->Fn);
    ComputeFk(lp_Kalman->Fn, lp_Kalman->Fk);
    
    // 状态预测
    ComputeXkk_1(lp_Kalman->Fk, lp_Kalman->Xk, lp_Kalman->Xkk_1);
    
    // 协方差预测
    ComputePkk_1(lp_Kalman->Fk, lp_Kalman->Pk, lp_Kalman->Qk, lp_Kalman->Pkk_1);
}

/*********************************函数说明***************************************/
/*函数名称：KalUpdate                                                            */
/*函数功能描述：Kalman滤波更新步骤                                               */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：指针:GNSS数据结构体lp_GNSSData                                       */
/*输入输出变量：指针:Kalman滤波结构体lp_Kalman                                   */
/*返回值：无                                                                     */
/*********************************************************************************/
void KalUpdate(p_GNSSData lp_GNSSData, p_Kalman lp_Kalman)
{
    // 计算观测向量
    ComputeZk(lp_GNSSData, lp_Kalman, lp_Kalman->Obv_Fit_Mode);
    
    // 计算卡尔曼增益
    ComputeKk(lp_Kalman->Pkk_1, lp_Kalman->Hk, lp_Kalman->Rk, lp_Kalman->Kk);
    
    // 状态更新
    ComputeXk(lp_Kalman->Xkk_1, lp_Kalman->Hk, lp_Kalman->Zk, lp_Kalman->Kk, lp_Kalman->Xk);
    
    // 协方差更新
    ComputePk(lp_Kalman->Pkk_1, lp_Kalman->Hk, lp_Kalman->Kk, lp_Kalman->Pk);
    
    // 误差修正
    lp_Kalman->isCorrectError = YES;
}
