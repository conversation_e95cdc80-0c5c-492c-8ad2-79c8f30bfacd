# HPM6750算法移植目录

本目录包含从HPM6750项目移植过来的导航算法代码。

## 目录结构

- `inav/` - 惯性导航算法模块
  - `navi.c` - 主要导航解算函数
  - `kalman.c` - 卡尔曼滤波算法
  - `align.c` - 初始对准算法
  - `sins.c` - 捷联惯导算法
  - `matvecmath.c` - 矩阵向量数学运算
  - `compen.c` - 传感器补偿算法
  - `ahrs.c` - 姿态航向参考系统
  - `ins.c` - 惯性导航系统主函数

- `nav/` - 导航应用层模块
  - `nav_cli.c` - 导航命令行接口

- `source/` - 源码支持模块
  - `INS912AlgorithmEntry.c` - 算法入口函数
  - `INS_Output.c` - 输出处理
  - `INS_Data.c` - 数据处理
  - `INS_Sys.c` - 系统管理

- `include/` - 头文件
  - `CONST.h` - 常量定义
  - `DATASTRUCT.h` - 数据结构定义
  - `FUNCTION.h` - 函数声明
  - `TYPEDEFINE.h` - 类型定义
  - `ins.h` - 惯导系统头文件

- `protocol/` - 协议处理模块
  - `InsTestingEntry.c` - 测试入口

## 移植说明

1. 保持HPM6750原有的算法逻辑不变
2. 适配组合算法后处理VS项目的数据结构
3. 修改接口以兼容现有系统
4. 暂时不对齐输入输出接口，专注于算法移植

## 编译说明

移植完成后需要：
1. 更新项目文件包含新的源文件
2. 解决编译依赖问题
3. 适配数据类型定义
4. 测试算法功能
