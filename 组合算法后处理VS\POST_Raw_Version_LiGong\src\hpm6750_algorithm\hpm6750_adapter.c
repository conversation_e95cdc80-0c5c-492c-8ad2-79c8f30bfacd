/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_adapter.c - HPM6750算法适配层                                                                                           */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：用于适配HPM6750算法与组合算法后处理VS项目的数据结构和接口差异                                                                   */
/*******************************************************************************************************************************************/

#include "include/hpm6750_types.h"
#include "include/hpm6750_datastruct.h"
#include "include/hpm6750_functions.h"
#include "../nav_type.h"
#include "../nav_const.h"

// HPM6750算法全局变量定义
SysVar g_SysVar;
InitBind g_InitBind;
Navi g_Navi;
Kalman g_Kalman;
GNSSData g_GNSSData_In_Use;
InertialSysAlign g_InertialSysAlign;

/*****************************函数说明*************************************/
/*函数名称：HPM6750_Algorithm_Init                                        */
/*函数功能描述：HPM6750算法初始化                                         */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：无                                                            */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void HPM6750_Algorithm_Init(void)
{
    // 系统变量初始化
    memset(&g_SysVar, 0, sizeof(SysVar));
    g_SysVar.WorkPhase = PHASE_SYS_INIT;
    g_SysVar.isGNSSValid = NO;
    g_SysVar.isDampedOK = NO;
    g_SysVar.IsVehicleConstraints = NO;
    
    // 初始装订参数设置 (可根据实际需求修改)
    g_InitBind.r_InitLati = 39.9042 * D2R;  // 北京纬度
    g_InitBind.r_InitLogi = 116.4074 * D2R; // 北京经度
    g_InitBind.InitHeight = 50.0;           // 初始高度50m
    g_InitBind.InitVn[0] = 0.0;             // 初始北向速度
    g_InitBind.InitVn[1] = 0.0;             // 初始天向速度
    g_InitBind.InitVn[2] = 0.0;             // 初始东向速度
    g_InitBind.isHeadBind = NO;             // 航向装订标志
    g_InitBind.r_InitHead = 0.0;            // 初始航向
    
    // 初始对准初始化
    InertialSysAlign_Init(&g_InitBind, &g_InertialSysAlign);
    
    // 导航结构体初始化
    Navi_Init(&g_InitBind, &g_InertialSysAlign, &g_Navi);
    
    // 卡尔曼滤波初始化
    Kalman_Init(&g_Kalman);
    
    // GNSS数据初始化
    memset(&g_GNSSData_In_Use, 0, sizeof(GNSSData));
}

/*****************************函数说明*************************************/
/*函数名称：Convert_IMU_To_HPM6750                                        */
/*函数功能描述：将组合算法后处理VS的IMU数据转换为HPM6750格式               */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：组合算法后处理VS的IMU数据指针                                 */
/*输出变量：HPM6750格式的陀螺和加速度计数据                               */
/*返回值：无                                                              */
/**************************************************************************/
void Convert_IMU_To_HPM6750(_IMU_t* pIMU, DPARA Gyro[3], DPARA LastGyro[3], DPARA Acc[3], DPARA LastAcc[3])
{
    int i;
    
    // 转换陀螺数据 (rad/s)
    for (i = 0; i < 3; i++)
    {
        Gyro[i] = pIMU->gyro_use[i];
        LastGyro[i] = pIMU->gyro_use_pre[i];
    }
    
    // 转换加速度计数据 (m/s^2)
    for (i = 0; i < 3; i++)
    {
        Acc[i] = pIMU->acc_use[i];
        LastAcc[i] = pIMU->acc_use_pre[i];
    }
}

/*****************************函数说明*************************************/
/*函数名称：Convert_GPS_To_HPM6750                                        */
/*函数功能描述：将组合算法后处理VS的GPS数据转换为HPM6750格式               */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：组合算法后处理VS的GPS数据指针                                 */
/*输出变量：HPM6750格式的GNSS数据                                         */
/*返回值：无                                                              */
/**************************************************************************/
void Convert_GPS_To_HPM6750(_GPS_UP_DAT* pGPS, p_GNSSData pGNSSData)
{
    // 位置数据转换
    pGNSSData->Lati = pGPS->Lat * D2R;      // 度转弧度
    pGNSSData->Logi = pGPS->Lon * D2R;      // 度转弧度
    pGNSSData->Height = pGPS->Altitude;     // 高度 (m)
    
    // 速度数据转换 (NED坐标系)
    pGNSSData->Vn[0] = pGPS->vn;           // 北向速度 (m/s)
    pGNSSData->Vn[1] = -pGPS->vu;          // 天向速度 (m/s, 注意符号)
    pGNSSData->Vn[2] = pGPS->ve;           // 东向速度 (m/s)
    
    // 航向数据转换
    pGNSSData->Heading = pGPS->Heading * D2R; // 度转弧度
    
    // 状态标志转换
    pGNSSData->isValid = (pGPS->Position_Status >= 1) ? YES : NO;
    pGNSSData->Position_Status = pGPS->Position_Status;
    pGNSSData->rtkStatus = pGPS->rtkStatus;
    pGNSSData->Sate_Num = pGPS->Sate_Num;
    
    // 时间信息
    pGNSSData->GnssTime = pGPS->gpssecond / 1000.0; // ms转s
    pGNSSData->HDOP = 1.0; // 默认值，可根据实际情况修改
}

/*****************************函数说明*************************************/
/*函数名称：Convert_HPM6750_To_NAV                                        */
/*函数功能描述：将HPM6750导航结果转换为组合算法后处理VS格式                */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：HPM6750格式的导航数据指针                                     */
/*输出变量：组合算法后处理VS格式的导航数据                                */
/*返回值：无                                                              */
/**************************************************************************/
void Convert_HPM6750_To_NAV(p_Navi pNaviIn, _NAV_t* pNavOut)
{
    int i;

    // 位置数据转换
    pNavOut->pos[0] = pNaviIn->d_Lati;      // 纬度 (度)
    pNavOut->pos[1] = pNaviIn->d_Logi;      // 经度 (度)
    pNavOut->pos[2] = pNaviIn->Height;      // 高度 (m)

    // 速度数据转换 (NED坐标系)
    pNavOut->vn[0] = pNaviIn->Vn[0];        // 北向速度 (m/s)
    pNavOut->vn[1] = pNaviIn->Vn[1];        // 天向速度 (m/s)
    pNavOut->vn[2] = pNaviIn->Vn[2];        // 东向速度 (m/s)

    // 姿态数据转换
    pNavOut->att[0] = pNaviIn->d_Atti[1];   // 俯仰角 (度)
    pNavOut->att[1] = pNaviIn->d_Atti[2];   // 横滚角 (度)
    pNavOut->att[2] = pNaviIn->d_Heading;   // 航向角 (度)

    // 姿态角速率转换
    for (i = 0; i < 3; i++)
    {
        pNavOut->att_rate[i] = pNaviIn->d_AttiRate[i]; // 姿态角速率 (度/s)
    }

    // 姿态矩阵转换
    for (i = 0; i < 9; i++)
    {
        pNavOut->Cnb[i] = pNaviIn->Cnb[i];
    }

    // 四元数转换
    for (i = 0; i < 4; i++)
    {
        pNavOut->qnb[i] = pNaviIn->Q[i];
    }
}

/*****************************函数说明*************************************/
/*函数名称：Update_NAV_Status_From_HPM6750                                */
/*函数功能描述：从HPM6750算法状态更新导航状态                             */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：导航数据结构体指针                                            */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void Update_NAV_Status_From_HPM6750(_NAV_Data_Full_t* pNavData)
{
    // 根据HPM6750算法的工作阶段更新导航状态
    switch(g_SysVar.WorkPhase)
    {
        case PHASE_SYS_INIT:
        case PHASE_STANDBY:
            // 系统初始化状态对应的导航状态需要根据实际定义调整
            break;

        case PHASE_COARSE_ALIGN:
        case PHASE_FINE_ALIGN:
            // 对准阶段
            break;

        case PHASE_INS_NAVI:
            // 纯惯导阶段
            break;

        case PHASE_INTEGRATED_NAVI:
            // 组合导航阶段
            break;

        case PHASE_FAIL:
            // 失败状态
            break;

        default:
            break;
    }

    // 更新GNSS有效性标志
    // pNavData->GPS.gps_up_flag = g_SysVar.isGNSSValid;
}

/*****************************函数说明*************************************/
/*函数名称：Get_HPM6750_Algorithm_Status                                  */
/*函数功能描述：获取HPM6750算法运行状态信息                               */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：无                                                            */
/*输出变量：状态信息字符串                                                */
/*返回值：无                                                              */
/**************************************************************************/
void Get_HPM6750_Algorithm_Status(char* status_str, int max_len)
{
    snprintf(status_str, max_len,
        "HPM6750 Status: Phase=%d, GNSS=%s, NavCount=%d, FilterCount=%d, AlignTime=%.1f",
        g_SysVar.WorkPhase,
        (g_SysVar.isGNSSValid == YES) ? "Valid" : "Invalid",
        g_Navi.Navi_Count,
        g_Kalman.Filter_Count,
        g_InertialSysAlign.AlignTime);
}

/*****************************函数说明*************************************/
/*函数名称：HPM6750_Algorithm_Process                                     */
/*函数功能描述：HPM6750算法主处理函数                                     */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：IMU数据、GPS数据                                              */
/*输出变量：导航结果                                                      */
/*返回值：无                                                              */
/**************************************************************************/
void HPM6750_Algorithm_Process(_IMU_t* pIMU, _GPS_UP_DAT* pGPS, _NAV_t* pNavOut)
{
    DPARA Gyro[3], LastGyro[3], Acc[3], LastAcc[3];
    
    // 数据格式转换
    Convert_IMU_To_HPM6750(pIMU, Gyro, LastGyro, Acc, LastAcc);
    Convert_GPS_To_HPM6750(pGPS, &g_GNSSData_In_Use);
    
    // 根据系统状态执行相应算法
    switch(g_SysVar.WorkPhase)
    {
        case PHASE_SYS_INIT:
            // 系统初始化阶段
            g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;
            break;
            
        case PHASE_COARSE_ALIGN:
            // 粗对准阶段
            InertialSysAlignCompute(Gyro, LastGyro, Acc, LastAcc, &g_InertialSysAlign);
            if(g_InertialSysAlign.AlignTime > 30.0) // 粗对准30秒
            {
                g_SysVar.WorkPhase = PHASE_FINE_ALIGN;
            }
            break;
            
        case PHASE_FINE_ALIGN:
            // 精对准阶段
            InertialSysAlignCompute(Gyro, LastGyro, Acc, LastAcc, &g_InertialSysAlign);
            if(g_InertialSysAlign.AlignTime > 180.0) // 总对准时间3分钟
            {
                // 对准完成，初始化导航
                Navi_Init(&g_InitBind, &g_InertialSysAlign, &g_Navi);
                g_SysVar.WorkPhase = PHASE_INS_NAVI;
            }
            break;
            
        case PHASE_INS_NAVI:
            // 纯惯导阶段
            NaviCompute(Gyro, LastGyro, Acc, LastAcc, &g_Navi);
            if(g_GNSSData_In_Use.isValid == YES)
            {
                g_SysVar.WorkPhase = PHASE_INTEGRATED_NAVI;
            }
            break;
            
        case PHASE_INTEGRATED_NAVI:
            // 组合导航阶段
            NaviCompute(Gyro, LastGyro, Acc, LastAcc, &g_Navi);
            KalCompute(&g_GNSSData_In_Use, &g_Kalman);
            if(g_Kalman.isCorrectError == YES)
            {
                ErrCorrect(&g_Navi, &g_Kalman, &g_GNSSData_In_Use);
                g_Kalman.isCorrectError = NO;
            }
            break;
            
        default:
            break;
    }
    
    // 输出结果转换
    Convert_HPM6750_To_NAV(&g_Navi, pNavOut);
}
