#ifndef _HPM6750_TYPES_H_
#define _HPM6750_TYPES_H_

/*
 * HPM6750算法移植 - 类型定义文件
 * 用于适配HPM6750算法到组合算法后处理VS项目
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <stdint.h>

// 基础数据类型定义 (来自HPM6750 TYPEDEFINE.h)
typedef double DPARA;
typedef double ANGRATE;
typedef double ACCELER;
typedef double VEL;
typedef double LATI;
typedef double LOGI;
typedef double HEIGHT;
typedef double LEN;
typedef double MATR;
typedef double QUAT;
typedef double ATTI;
typedef double DELANG;
typedef double VEC;
typedef double TIME;
typedef int IPARA;
typedef unsigned char MODE;

// 常量定义 (来自HPM6750 CONST.h)
#ifndef PI
#define PI (3.1415926535897932384626433832795)
#endif

#define PIDIV2 (1.5707963267948966192313216916398)
#define PIMUL2 (6.283185307179586476925286766559)
#define R2D (57.295779513082320876798154814105)
#define D2R (1.7453292519943295769236907684886e-2)

#define WIE (7.292115147e-5)
#define G0 (9.78049)
#define RE (6378137.0)
#define INVRE (1.5678559428873979972521756744955e-7)

#define MIN_DATA (1.0e-18)
#define YES (0x55)
#define NO (0xAA)

// 时间常量
#define TIME_NAVI (0.005)
#define TIME_FILTER (0.2)

// 系统工作阶段定义
#define PHASE_SYS_INIT (0x70)
#define PHASE_STANDBY (0x71)
#define PHASE_CALI (0x73)
#define PHASE_FAIL (0x74)
#define PHASE_COARSE_ALIGN (0x75)
#define PHASE_FINE_ALIGN (0x76)
#define PHASE_INS_NAVI (0x77)
#define PHASE_INTEGRATED_NAVI (0x78)

// Kalman滤波相关常量
#define DIM_STATE (15)
#define DIM_MAX_OBV (7)

// 缓冲区大小定义
#define HEADINGRATE_BUFFER_SIZE (200)

// 阈值定义
#define THRESHOLD_HEADING_RATE (1.5)
#define THRESHOLD_GYRO (800.0)
#define THRESHOLD_ACC (30.0 * G0)

// 修正相关常量
#define POSITION_CORRECT_COUNT (1)
#define VELOCITY_CORRECT_COUNT (10)
#define ATTITUDE_CORRECT_COUNT (50)
#define HEADING_CORRECT_COUNT (5)

// 工作模式定义
#define PREDICT (1)
#define FILTER (2)

// 观测拟合模式定义
#define POSVEL_FIT (1)
#define POSVELHEAD_FIT (2)
#define ZERO_SPEED (3)
#define VEHICLECONSTRAINTS_FIT (4)

// 速度阈值定义
#define THRESHOLD_VN (50.0)
#define THRESHOLD_VE (50.0)

// 计数阈值定义
#define COUNT_RESET_KALMAN_FILTER (1000)

// 对准时间常量
#define TIME_COARSE_ALIGN (30.0)
#define TIME_FINE_ALIGN (180.0)

#endif // _HPM6750_TYPES_H_
