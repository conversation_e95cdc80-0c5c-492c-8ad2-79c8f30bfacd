﻿  computerFrameParse.c
  fpgad.c
  gnss.c
  main.c
  nav.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(123,13): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(788,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(869,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(885,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(941,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(997,14): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1252,4): warning C4013: “wheel_is_running”未定义；假设外部返回 int
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\main.c(1255,6): warning C4013: “HPM6750_Algorithm_Process”未定义；假设外部返回 int
  navlog.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(177,15): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(183,15): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(189,18): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\navlog.c(194,15): warning C4267: “=”: 从“size_t”转换到“unsigned int”，可能丢失数据
  nav_app.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(43,9): warning C4005: “WIE”: 宏重定义
  (编译源文件“../src/nav_app.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_const.h(27,10):
      参见“WIE”的前一个定义
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(44,9): warning C4005: “G0”: 宏重定义
  (编译源文件“../src/nav_app.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_const.h(31,11):
      参见“G0”的前一个定义
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/nav_app.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(75,6): warning C4031: 第二个形参表比第一个长
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(75,6): warning C4028: 形参 1 与声明不一致
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(75,6): warning C4028: 形参 2 与声明不一致
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(75,6): warning C4028: 形参 3 与声明不一致
  (编译源文件“../src/nav_app.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(406,2): warning C4013: “PrintOutStateChange”未定义；假设外部返回 int
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(589,39): warning C4133: “函数”: 从“_IMU_t *”到“const DPARA *”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(589,59): warning C4133: “函数”: 从“_GPS_t *”到“const DPARA *”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(589,79): warning C4133: “函数”: 从“_SINS_t *”到“p_GNSSData”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(589,13): error C2198: “void HPM6750_Algorithm_Process(const DPARA *,const DPARA *,p_GNSSData,p_Navi)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(592,17): error C2065: “g_SysVar”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(592,26): error C2224: “.WorkPhase”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(595,36): error C2039: "Align_flag": 不是 "_SINS_t" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_type.h(149,1):
      参见“_SINS_t”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(600,36): error C2039: "Align_flag": 不是 "_SINS_t" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_type.h(149,1):
      参见“_SINS_t”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(608,39): warning C4133: “函数”: 从“_IMU_t *”到“const DPARA *”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(608,59): warning C4133: “函数”: 从“_GPS_t *”到“const DPARA *”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(608,79): warning C4133: “函数”: 从“_SINS_t *”到“p_GNSSData”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(608,13): error C2198: “void HPM6750_Algorithm_Process(const DPARA *,const DPARA *,p_GNSSData,p_Navi)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(611,13): warning C4013: “Update_NAV_Status_From_HPM6750”未定义；假设外部返回 int
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(633,5): warning C4013: “Get_HPM6750_Algorithm_Status”未定义；假设外部返回 int
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_app.c(633,34): error C2065: “hmp6750_status”: 未声明的标识符
  nav_cli.c
  nav_gnss.c
  nav_imu.c
  nav_kf.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(3709,9): warning C4101: “posL_”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(3708,9): warning C4101: “vnL_”: 未引用的局部变量
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_kf.c(3706,22): warning C4101: “MpvVn”: 未引用的局部变量
  nav_magnet.c
  nav_mahony.c
  nav_math.c
  nav_ods.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_ods.c(313,10): warning C4101: “k”: 未引用的局部变量
  nav_sins.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_sins.c(1298,8): warning C4101: “j”: 未引用的局部变量
  nav_uwb.c
  hpm6750_adapter.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/hpm6750_adapter.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(15,8): error C2079: “g_SysVar”使用未定义的 struct“_SysVar”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(16,10): error C2079: “g_InitBind”使用未定义的 struct“_InitBind”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(17,6): error C2079: “g_Navi”使用未定义的 struct“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(19,10): error C2079: “g_GNSSData_In_Use”使用未定义的 struct“_GNSSData”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(20,18): error C2079: “g_InertialSysAlign”使用未定义的 struct“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(34,14): error C2224: “.WorkPhase”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(35,14): error C2224: “.isGNSSValid”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(36,14): error C2224: “.isDampedOK”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(37,14): error C2224: “.IsVehicleConstraints”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(40,16): error C2224: “.r_InitLati”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(41,16): error C2224: “.r_InitLogi”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(42,16): error C2224: “.InitHeight”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(43,16): error C2224: “.InitVn”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(44,16): error C2224: “.InitVn”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(45,16): error C2224: “.InitVn”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(46,16): error C2224: “.isHeadBind”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(47,16): error C2224: “.r_InitHead”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(52,15): warning C4133: “函数”: 从“int *”到“const p_InitBind”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(52,28): warning C4133: “函数”: 从“int *”到“const p_InertialSysAlign”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(52,49): warning C4133: “函数”: 从“int *”到“p_Navi”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(97,40): error C2143: 语法错误: 缺少“)”(在“*”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(97,40): error C2143: 语法错误: 缺少“{”(在“*”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(97,48): error C2040: “p_GNSSData”:“int”与“GNSSData *”的间接寻址级别不同
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(97,59): error C2146: 语法错误: 缺少“;”(在标识符“pGNSSData”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(97,68): error C2059: 语法错误:“)”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_adapter.c(97,59): error C2054: 在“pGNSSData”之后应输入“(”
  hpm6750_integration.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_math.h(49,6): warning C4030: 第一个形参表比第二个长
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_math.h(49,6): warning C4028: 形参 1 与声明不一致
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_math.h(49,6): warning C4028: 形参 2 与声明不一致
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\nav_math.h(49,6): warning C4028: 形参 3 与声明不一致
  (编译源文件“../src/hpm6750_algorithm/hpm6750_integration.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(41,9): error C2065: “g_HPM6750_Initialized”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(59,17): error C2065: “g_SysVar”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(59,26): error C2224: “.WorkPhase”的左侧必须具有结构/联合类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(70,39): warning C4133: “函数”: 从“_IMU_t *”到“const DPARA *”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(70,59): warning C4133: “函数”: 从“_GPS_t *”到“const DPARA *”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(70,79): warning C4133: “函数”: 从“_SINS_t *”到“p_GNSSData”的类型不兼容
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(70,13): error C2198: “void HPM6750_Algorithm_Process(const DPARA *,const DPARA *,p_GNSSData,p_Navi)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(65,9): error C2043: 非法 break
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(67,9): error C2046: 非法的 case
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(74,9): error C2043: 非法 break
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(76,9): error C2046: 非法的 case
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(80,9): error C2043: 非法 break
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(82,9): error C2047: 非法的 default
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(83,13): error C2043: 非法 break
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(85,5): error C2059: 语法错误:“}”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(93,17): error C2143: 语法错误: 缺少“)”(在“&”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(93,17): error C2143: 语法错误: 缺少“{”(在“&”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(93,17): error C2059: 语法错误:“&”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(93,30): error C2059: 语法错误:“)”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(94,1): error C2059: 语法错误:“}”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\hpm6750_integration.c(173,1): error C1004: 发现意外的文件尾
  hpm6750_navi.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_navi.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(36,24): error C2027: 使用了未定义类型“_Navi”
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(14,16):
      参见“_Navi”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(38,5): error C2037: “r_Logi”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(38,23): error C2037: “r_InitLogi”的左侧部分指定未定义的结构/联合“_InitBind”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(39,5): error C2037: “Height”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(39,23): error C2037: “InitHeight”的左侧部分指定未定义的结构/联合“_InitBind”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(43,9): error C2037: “Vn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(43,26): error C2037: “InitVn”的左侧部分指定未定义的结构/联合“_InitBind”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(49,9): error C2037: “Cnb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(49,27): error C2037: “AlignCnb”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(55,9): error C2037: “Q”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(55,25): error C2037: “AlignQ”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(77,9): error C2037: “r_Wibb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(78,9): error C2037: “r_Wibb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(79,9): error C2037: “Fibb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(80,9): error C2037: “Fibb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(84,17): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(84,34): error C2037: “r_Wien”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(84,5): error C2198: “void ComputeWien(LATI,ANGRATE *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,17): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,34): error C2037: “Height”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,52): error C2037: “Rm”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,66): error C2037: “Rn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,80): error C2037: “invRm”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,97): error C2037: “invRn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(86,5): error C2198: “void ComputeRmRn(LATI,HEIGHT,LEN *,LEN *,DPARA *,DPARA *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(88,17): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(88,34): error C2037: “Vn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(88,47): error C2037: “invRm”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(88,63): error C2037: “invRn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(88,79): error C2037: “r_Wenn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(88,5): error C2198: “void ComputeWenn(LATI,const VEL *,DPARA,DPARA,ANGRATE *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(90,5): warning C4013: “ComputeWnbb”未定义；假设外部返回 int
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(90,17): error C2037: “r_Wibb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(90,34): error C2037: “r_Wien”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(90,51): error C2037: “r_Wenn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(90,68): error C2037: “Cnb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(90,82): error C2037: “r_Wnbb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(92,21): error C2037: “r_Wnbb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(92,38): error C2037: “r_DelSenbb_1”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(92,61): error C2037: “r_DelSenbb_2”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(92,84): error C2037: “r_DelSenbb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(92,5): error C2198: “void ComputeDelSenbb(ANGRATE (*)[3],DELANG *,DELANG *,DELANG *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(94,14): error C2037: “r_DelSenbb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(94,35): error C2037: “Q”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(94,5): error C2198: “void ComputeQ(DELANG *,QUAT *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(96,12): error C2037: “Q”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(96,24): error C2037: “Cnb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(96,5): error C2198: “void QToCnb(const QUAT *,MATR *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(97,15): error C2037: “Cnb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(97,29): error C2037: “r_Atti”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(97,5): error C2198: “void CnbToAtti(const MATR *,ATTI *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(99,21): error C2037: “r_Wnbb”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(99,38): error C2037: “r_Atti”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(99,55): error C2037: “r_AttiRate”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(99,5): error C2198: “void ComputeAttiRate(ANGRATE (*)[3],const ATTI *,ANGRATE *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(101,14): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(101,31): error C2037: “Height”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(101,49): error C2037: “Gn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(101,5): error C2198: “void ComputeG(LATI,HEIGHT,ACCELER *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(108,8): error C2037: “IsVehicleConstraints”的左侧部分指定未定义的结构/联合“_SysVar”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,16): error C2037: “LastVn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,33): error C2037: “Vn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,46): error C2037: “invRm”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,62): error C2037: “invRn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,79): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,97): error C2037: “r_Logi”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,115): error C2037: “Height”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,132): error C2037: “Sn”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(113,5): error C2198: “void ComputePos(const VEL *,const VEL *,DPARA,DPARA,LATI *,LOGI *,HEIGHT *,LEN *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(119,5): error C2037: “d_HeadingRateBuffer”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(119,34): error C2037: “HeadingRate_Circle_Count”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(119,71): error C2037: “d_AttiRate”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(120,5): error C2037: “HeadingRate_Circle_Count”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(122,8): error C2037: “HeadingRate_Circle_Count”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(122,45): error C2065: “HEADINGRATE_BUFFER_SIZE”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(124,9): error C2037: “HeadingRate_Circle_Count”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(126,5): error C2037: “d_HeadingRate_Mean”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(127,20): error C2065: “HEADINGRATE_BUFFER_SIZE”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(129,9): error C2037: “d_HeadingRate_Mean”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(129,40): error C2037: “d_HeadingRateBuffer”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(132,5): error C2037: “d_HeadingRate_Mean”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(132,36): error C2065: “HEADINGRATE_BUFFER_SIZE”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(134,13): error C2037: “d_HeadingRate_Mean”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(134,8): error C2168: “fabs”: 内部函数的实参太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(134,45): error C2065: “THRESHOLD_HEADING_RATE”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(136,9): error C2037: “isHeadingchange”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(140,9): error C2037: “isHeadingchange”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(144,9): error C2037: “Correct_Count”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(144,41): error C2037: “WorkPhase”的左侧部分指定未定义的结构/联合“_SysVar”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(148,5): error C2037: “Navi_Count”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(217,6): error C2371: “ComputeWnbb”: 重定义；不同的基类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(384,5): error C2181: 没有匹配 if 的非法 else 
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(399,5): error C2181: 没有匹配 if 的非法 else 
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(449,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(450,6): error C2065: “lp_Gn”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(450,5): error C2100: 无法取消引用类型为“int”的操作数
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(450,111): warning C4244: “=”: 从“double”转换到“int”，可能丢失数据
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(463,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(473,19): error C2065: “lp_Navi”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_navi.c(473,19): error C1003: 错误计数超过 100；正在停止编译
  正在编译...
  hpm6750_kalman.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(38,24): error C2039: "Pk": 不是 "_Kalman" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(111,16):
      参见“_Kalman”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(38,5): error C2198: “void Pk_Init(MATR *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(46,16): error C2039: "Filter_Count": 不是 "_Kalman" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(111,16):
      参见“_Kalman”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(79,20): error C2039: "Filter_Count": 不是 "_Kalman" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(111,16):
      参见“_Kalman”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(94,26): error C2039: "Fn": 不是 "_Kalman" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(111,16):
      参见“_Kalman”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(94,41): error C2039: "Fk": 不是 "_Kalman" 的成员
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(111,16):
      参见“_Kalman”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman.c(94,5): error C2198: “void ComputeFk(MATR *,MATR *)”: 用于调用的参数太少
  hpm6750_align.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_align.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(23,36): error C2027: 使用了未定义类型“_InertialSysAlign”
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,16):
      参见“_InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(26,5): error C2037: “r_Logi”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(26,35): error C2037: “r_InitLogi”的左侧部分指定未定义的结构/联合“_InitBind”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(27,5): error C2037: “Height”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(27,35): error C2037: “InitHeight”的左侧部分指定未定义的结构/联合“_InitBind”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(30,5): error C2037: “Qbib0”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(31,5): error C2037: “Qbib0”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(32,5): error C2037: “Qbib0”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(35,16): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(35,45): error C2037: “r_Logi”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(35,74): error C2037: “Cen”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(35,5): error C2198: “void ComputeCen(LATI,LOGI,MATR *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(54,9): error C2037: “r_Wibb”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(55,9): error C2037: “r_Wibb”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(59,16): error C2037: “AlignTime”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(59,48): error C2037: “Cie”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(59,5): error C2198: “void ComputeCie(TIME,MATR *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(61,21): error C2037: “r_Wibb”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(61,50): error C2037: “r_DelSenbb_1”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(62,21): error C2037: “r_DelSenbb_2”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(62,56): error C2037: “r_DelSenbb”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(61,5): error C2198: “void ComputeDelSenbb(ANGRATE (*)[3],DELANG *,DELANG *,DELANG *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(64,14): error C2037: “r_DelSenbb”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(64,47): error C2037: “Qbib0”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(64,5): error C2198: “void ComputeQ(DELANG *,QUAT *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(66,12): error C2037: “Qbib0”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(66,40): error C2037: “Cib0b”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(66,5): error C2198: “void QToCnb(const QUAT *,MATR *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,12): error C2037: “Cib0b”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,40): error C2037: “Cbib0”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,68): warning C4047: “函数”:“const MATR *”与“int”的间接级别不同
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,68): warning C4024: “Mat_Tr”: 形参和实参 1 的类型不同
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,71): warning C4047: “函数”:“MATR *”与“int”的间接级别不同
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,71): warning C4024: “Mat_Tr”: 形参和实参 2 的类型不同
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(68,5): error C2198: “void Mat_Tr(const MATR *,MATR *,IPARA,IPARA)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(70,14): error C2037: “r_Lati”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(70,43): error C2037: “Height”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(70,73): error C2037: “Gn”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(70,5): error C2198: “void ComputeG(LATI,HEIGHT,ACCELER *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(71,15): error C2037: “Gn”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(71,40): error C2037: “Cie”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(71,66): error C2037: “Cen”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(71,92): error C2037: “Vi”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(71,5): error C2198: “void ComputeVi(ACCELER,MATR *,MATR *,VEL *)”: 用于调用的参数太少
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_align.c(73,5): error C2037: “AlignTime”的左侧部分指定未定义的结构/联合“_InertialSysAlign”
  hpm6750_math.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_math.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(142,9): error C2037: “d_Atti”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(142,30): error C2037: “r_Atti”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(146,5): error C2037: “d_Logi”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(146,23): error C2037: “r_Logi”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(151,9): error C2037: “d_AttiRate”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(151,34): error C2037: “r_AttiRate”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(170,9): error C2037: “d_Heading”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(172,12): error C2037: “d_Heading”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(174,9): error C2037: “d_Heading”的左侧部分指定未定义的结构/联合“_Navi”
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_math.c(191,1): error C1004: 发现意外的文件尾
  hpm6750_kalman_init.c
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_types.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(37,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(46,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(108,1): error C2059: 语法错误:“}”
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(146,3): error C2371: “InertialSysAlign”: 重定义；不同的基类型
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
      E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_datastruct.h(17,34):
      参见“InertialSysAlign”的声明
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\include\hpm6750_functions.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“../src/hpm6750_algorithm/inav/hpm6750_kalman_init.c”)
  
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(56,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(61,9): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(61,11): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(66,5): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(66,7): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(67,5): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(67,7): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(68,5): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(68,7): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(71,5): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(71,7): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(72,5): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(72,7): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(73,5): error C2065: “Qk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(73,7): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(89,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(94,9): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(94,11): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(97,12): error C2065: “Obv_Fit_Mode”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(101,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(101,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(102,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(102,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(103,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(103,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(105,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(105,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(106,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(106,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(107,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(107,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(112,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(112,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(113,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(113,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(114,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(114,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(116,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(116,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(117,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(117,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(118,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(118,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(120,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(120,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(124,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(124,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(125,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(125,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(126,13): error C2065: “Rk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(126,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(144,1): error C2143: 语法错误: 缺少“;”(在“{”的前面)
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(149,9): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(149,11): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(152,12): error C2065: “Obv_Fit_Mode”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(156,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(156,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(157,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(157,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(158,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(158,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(160,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(160,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(161,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(161,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(162,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(162,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(167,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(167,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(168,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(168,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(169,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(169,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(171,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(171,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(172,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(172,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(173,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(173,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(175,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(175,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(178,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(178,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(179,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(179,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(180,13): error C2065: “Hk”: 未声明的标识符
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(180,15): error C2109: 下标要求数组或指针类型
E:\INS_Code\组合算法后处理VS(1) - 副本\组合算法后处理VS\POST_Raw_Version_LiGong\src\hpm6750_algorithm\inav\hpm6750_kalman_init.c(187,1): error C1004: 发现意外的文件尾
