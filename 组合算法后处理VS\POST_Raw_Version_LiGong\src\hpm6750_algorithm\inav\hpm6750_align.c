/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_align.c - HPM6750初始对准算法移植版本                                                                                       */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：从HPM6750项目INAV/align.c移植而来，适配组合算法后处理VS项目                                                                      */
/*******************************************************************************************************************************************/

#include "../include/hpm6750_types.h"
#include "../include/hpm6750_datastruct.h"
#include "../include/hpm6750_functions.h"

/*********************************************函数说明*******************************************************/
/*函数名称：InertialSysAlign_Init                                                                           */
/*函数功能描述：惯性凝固坐标系对准初始化                                                                    */
/*版本号：  Ver 1.0                                                                                         */
/*移植日期：2025-01-18                                                                                       */
/*输入变量：初始装订结构体指针lp_InitBind                                                                   */
/*输出变量：惯性系对准结构体指针lp_InertialSysAlign                                                         */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void InertialSysAlign_Init(p_InitBind const lp_InitBind, p_InertialSysAlign lp_InertialSysAlign)
{
    memset(lp_InertialSysAlign, 0, sizeof(InertialSysAlign));   //将结构体成员变量全部清零
    
    //惯导位置初始化
    lp_InertialSysAlign->r_Lati = lp_InitBind->r_InitLati;  //纬度,单位：rad
    lp_InertialSysAlign->r_Logi = lp_InitBind->r_InitLogi;  //经度，单位：rad
    lp_InertialSysAlign->Height = lp_InitBind->InitHeight;  //高度，单位：m
    
    //Qbib0的初始化，Cbib0以及Cib0b根据四元数计算获得，无需初始化
    lp_InertialSysAlign->Qbib0[0] = 1.0;
    lp_InertialSysAlign->Qbib0[1] = 0.0;
    lp_InertialSysAlign->Qbib0[2] = 0.0;
    lp_InertialSysAlign->Qbib0[3] = 0.0;

    //静基座对准条件下Cen取决于经纬度，不随对准时间改变，直接初始化
    //计算Cen方向余弦矩阵
    ComputeCen(lp_InertialSysAlign->r_Lati, lp_InertialSysAlign->r_Logi, lp_InertialSysAlign->Cen); 
}

/*********************************************函数说明*******************************************************/
/*函数名称：InertialSysAlignCompute                                                                         */
/*函数功能描述：计算惯性凝固坐标系对准的实时计算部分                                                        */
/*版本号：  Ver 1.0                                                                                         */
/*移植日期：2025-01-18                                                                                       */
/*输入变量：陀螺输出Gyro、LastGyro，加速度计输出Acc、LastAcc                                               */
/*输入输出变量：惯性系对准结构体指针lp_InertialSysAlign                                                     */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void InertialSysAlignCompute(DPARA const Gyro[3], DPARA const LastGyro[3], DPARA const Acc[3],
                             DPARA const LastAcc[3], p_InertialSysAlign lp_InertialSysAlign) 
{
    register IPARA i;
    
    //输入陀螺、加表数值的写入和转换
    for (i = 0; i < 3; i++)
    {
        lp_InertialSysAlign->r_Wibb[0][i] = LastGyro[i];
        lp_InertialSysAlign->r_Wibb[1][i] = Gyro[i];
    }
    
    //对准计算
    ComputeCie(lp_InertialSysAlign->AlignTime, lp_InertialSysAlign->Cie);

    ComputeDelSenbb(lp_InertialSysAlign->r_Wibb, lp_InertialSysAlign->r_DelSenbb_1, 
                    lp_InertialSysAlign->r_DelSenbb_2, lp_InertialSysAlign->r_DelSenbb);

    ComputeQ(lp_InertialSysAlign->r_DelSenbb, lp_InertialSysAlign->Qbib0);//复用navi.c的computeQ函数计算Qbib0

    QToCnb(lp_InertialSysAlign->Qbib0, lp_InertialSysAlign->Cib0b);//复用navi.c的QToCnb计算Cib0b

    Mat_Tr(lp_InertialSysAlign->Cib0b, lp_InertialSysAlign->Cbib0, 3, 3);//计算Cbib0

    ComputeG(lp_InertialSysAlign->r_Lati, lp_InertialSysAlign->Height, &lp_InertialSysAlign->Gn);//复用navi.c的ComputeG计算重力值

    ComputeVi(lp_InertialSysAlign->Gn, lp_InertialSysAlign->Cie, lp_InertialSysAlign->Cen, lp_InertialSysAlign->Vi);

    lp_InertialSysAlign->AlignTime += TIME_NAVI;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeCen                                                                                      */
/*函数功能描述：计算Cen方向余弦矩阵                                                                         */
/*版本号：  Ver 1.0                                                                                         */
/*移植日期：2025-01-18                                                                                       */
/*输入变量：纬度r_Lati，经度r_Logi                                                                          */
/*输出变量：方向余弦矩阵Cen                                                                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeCen(LATI r_Lati, LOGI r_Logi, MATR Cen[9])
{
    register DPARA SINLATI = sin(r_Lati);
    register DPARA COSLATI = cos(r_Lati);
    register DPARA SINLOGI = sin(r_Logi);
    register DPARA COSLOGI = cos(r_Logi);

    Cen[0] = -1 * SINLATI * COSLOGI;
    Cen[1] = -1 * SINLATI * SINLOGI;
    Cen[2] = COSLATI;
    Cen[3] =  COSLATI * COSLOGI;
    Cen[4] = COSLATI * SINLOGI;
    Cen[5] = SINLATI;
    Cen[6] = -1 * SINLOGI;
    Cen[7] =  COSLOGI;
    Cen[8] = 0;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeCie                                                                                      */
/*函数功能描述：计算Cie方向余弦矩阵                                                                         */
/*版本号：  Ver 1.0                                                                                         */
/*移植日期：2025-01-18                                                                                       */
/*输入变量：对准时间AlignTime                                                                               */
/*输出变量：方向余弦矩阵Cie                                                                                 */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeCie(TIME AlignTime, MATR Cie[9])
{
    Cie[0] = cos(WIE * AlignTime);
    Cie[1] = sin(WIE * AlignTime);
    Cie[2] = 0.0;

    Cie[3] = -1 * sin(WIE * AlignTime);
    Cie[4] = cos(WIE * AlignTime);
    Cie[5] = 0.0;

    Cie[6] = 0.0;
    Cie[7] = 0.0;
    Cie[8] = 1.0;
}

/*********************************************函数说明*******************************************************/
/*函数名称：ComputeVi                                                                                       */
/*函数功能描述：计算i系比力积分累积量Vi                                                                     */
/*版本号：  Ver 1.0                                                                                         */
/*移植日期：2025-01-18                                                                                       */
/*输入变量：重力加速度Gn，方向余弦矩阵Cie、Cen                                                             */
/*输出变量：惯性系速度Vi                                                                                    */
/*返回值：无                                                                                                */
/************************************************************************************************************/
void ComputeVi(ACCELER Gn, MATR Cie[9], MATR Cen[9], VEL Vi[3])
{
    IPARA i;
    MATR Cei[9];
    ACCELER Fn[3];
    ACCELER Fe[3];
    ACCELER Fi[3];

    Fn[0] = 0.0;
    Fn[1] = -Gn;
    Fn[2] = 0.0;

    // 计算Cei = Cie^T
    Mat_Tr(Cie, Cei, 3, 3);
    
    // 计算Fe = Cen^T * Fn
    Mat_Mul(Cen, Fn, Fe, 3, 3, 1);
    
    // 计算Fi = Cei * Fe
    Mat_Mul(Cei, Fe, Fi, 3, 3, 1);
    
    // 积分计算Vi
    for (i = 0; i < 3; i++)
    {
        Vi[i] += Fi[i] * TIME_NAVI;
    }
}
