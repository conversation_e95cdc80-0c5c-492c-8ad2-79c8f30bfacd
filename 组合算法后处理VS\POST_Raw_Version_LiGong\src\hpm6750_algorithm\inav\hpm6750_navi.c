/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_navi.c - HPM6750导航算法移植版本                                                                                           */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：从HPM6750项目INAV/navi.c移植而来，适配组合算法后处理VS项目                                                                        */
/*包含文件：hpm6750_types.h、hpm6750_datastruct.h、hpm6750_functions.h                                                                      */
/*******************************************************************************************************************************************/

#include "../include/hpm6750_types.h"
#include "../include/hpm6750_datastruct.h"
#include "../include/hpm6750_functions.h"

// 全局变量声明 (移植时需要适配)
extern SysVar g_SysVar;
extern InitBind g_InitBind;
extern Navi g_Navi;
extern Kalman g_Kalman;
extern GNSSData g_GNSSData_In_Use;
extern InertialSysAlign g_InertialSysAlign;

/*****************************函数说明*************************************/
/*函数名称：Navi_Init                                                     */
/*函数功能描述：完成惯导结构体的初始化                                     */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：指针:初始装订结构体lp_InitBind、指针：初始对准结构体lp_InertialSysAlign */
/*输出变量：指针:惯性导航结构体:lp_Navi                                    */
/*备注1：请在惯导解算开始前调用该函数进行导航结构体的初始化                 */
/*备注2：该函数所有形参为指针变量，请使用地址传递方式调用该函数              */
/*返回值：无                                                              */
/**************************************************************************/
void Navi_Init(p_InitBind const lp_InitBind, p_InertialSysAlign const lp_InertialSysAlign, p_Navi lp_Navi)
{
    register IPARA i;

    memset(lp_Navi, 0, sizeof(Navi));   //将g_Navi结构体成员变量全部清零

    //惯导位置初始化
    lp_Navi->r_Lati = lp_InitBind->r_InitLati;  //纬度,单位：rad
    lp_Navi->r_Logi = lp_InitBind->r_InitLogi;  //经度，单位：rad
    lp_Navi->Height = lp_InitBind->InitHeight;  //高度

    //惯导速度初始化
    for (i = 0; i < 3; i++)
    {
        lp_Navi->Vn[i] = lp_InitBind->InitVn[i];   
    }

    //姿态矩阵初始化
    for (i = 0; i < 9; i++)
    {
        lp_Navi->Cnb[i] = lp_InertialSysAlign->AlignCnb[i];
    }

    //姿态角初始化
    CnbToAtti(lp_Navi->Cnb, lp_Navi->r_Atti);
    //姿态四元数初始化
    for (i = 0; i < 4; i++)
    {
        lp_Navi->Q[i] = lp_InertialSysAlign->AlignQ[i];
    }

    ComputeDeg_Ex(lp_Navi);//计算角度表示的导航参数
    TransHeading0to360(lp_Navi);
}

/******************************************函数说明***********************************************/
/*函数名称：NaviCompute                                                                          */
/*函数功能描述：完成一次实时惯导解算                                                              */
/*版本号：  Ver 1.0                                                                              */
/*移植日期：2025-01-18                                                                            */
/*输入变量：参数：陀螺补偿输出Gyro、LastGyro、加速度计补偿输出Acc、LastAcc                        */
/*输出变量：指针:惯性导航结构体:lp_Navi                                                           */
/*备注1：该函数输入变量、输出变量均为地址传递                                                      */
/*备注2：该函数为惯导解算全流程的封装函数，用于实时惯导解算                                        */
/*返回值：无                                                                                     */
/*************************************************************************************************/
void NaviCompute(DPARA const Gyro[3], DPARA const LastGyro[3], DPARA const Acc[3], DPARA const LastAcc[3], p_Navi lp_Navi) 
{
    register IPARA i;	  
    //输入陀螺、加表数值的写入和转换
    for (i = 0; i < 3; i++)
    {
        lp_Navi->r_Wibb[0][i] = LastGyro[i];
        lp_Navi->r_Wibb[1][i] = Gyro[i];
        lp_Navi->Fibb[0][i] = LastAcc[i];
        lp_Navi->Fibb[1][i] = Acc[i]; 
    }

    //惯导解算过程
    ComputeWien(lp_Navi->r_Lati, lp_Navi->r_Wien);//计算地球自转角速率在导航系下的分量

    ComputeRmRn(lp_Navi->r_Lati, lp_Navi->Height, &lp_Navi->Rm, &lp_Navi->Rn, &lp_Navi->invRm, &lp_Navi->invRn);//计算地球子午圈半径、卯酉圈半径及其倒数

    ComputeWenn(lp_Navi->r_Lati, lp_Navi->Vn, lp_Navi->invRm, lp_Navi->invRn, lp_Navi->r_Wenn);//计算载体相对于地球表面的转移角速率在导航系下的分量

    ComputeWnbb(lp_Navi->r_Wibb, lp_Navi->r_Wien, lp_Navi->r_Wenn, lp_Navi->Cnb, lp_Navi->r_Wnbb);//计算载体相对于导航坐标系的旋转角速率在导航系下的分量

    ComputeDelSenbb(lp_Navi->r_Wnbb, lp_Navi->r_DelSenbb_1, lp_Navi->r_DelSenbb_2, lp_Navi->r_DelSenbb);

    ComputeQ(lp_Navi->r_DelSenbb, lp_Navi->Q);//计算姿态四元数

    QToCnb(lp_Navi->Q, lp_Navi->Cnb);//根据姿态四元数计算姿态矩阵

    CnbToAtti(lp_Navi->Cnb, lp_Navi->r_Atti);//根据姿态矩阵计算姿态角

    ComputeAttiRate(lp_Navi->r_Wnbb, lp_Navi->r_Atti, lp_Navi->r_AttiRate);//计算姿态角速率

    ComputeG(lp_Navi->r_Lati, lp_Navi->Height, &lp_Navi->Gn);//计算g

    ComputeVibn(lp_Navi);

    ComputeVn(lp_Navi);//计算导航坐标系下的载体速度

    // 载体约束处理 (需要适配全局变量)
    if(g_SysVar.IsVehicleConstraints == YES)
    {
        ComputeVnConstraints(&g_Navi);   
    }

    ComputePos(lp_Navi->LastVn, lp_Navi->Vn, lp_Navi->invRm, lp_Navi->invRn, &lp_Navi->r_Lati, &lp_Navi->r_Logi, &lp_Navi->Height, lp_Navi->Sn);//计算载体位置

    ComputeDeg_Ex(lp_Navi);//计算角度表示的导航参数

    TransHeading0to360(lp_Navi);
    
    //判断拐弯
    lp_Navi->d_HeadingRateBuffer[lp_Navi->HeadingRate_Circle_Count] = lp_Navi->d_AttiRate[0];
    lp_Navi->HeadingRate_Circle_Count++;

    if(lp_Navi->HeadingRate_Circle_Count == HEADINGRATE_BUFFER_SIZE)
    {
        lp_Navi->HeadingRate_Circle_Count = 0;
    }
    lp_Navi->d_HeadingRate_Mean = 0.0;//清0
    for(i = 0; i < HEADINGRATE_BUFFER_SIZE; i++)
    {
        lp_Navi->d_HeadingRate_Mean += lp_Navi->d_HeadingRateBuffer[i];
    }

    lp_Navi->d_HeadingRate_Mean /= HEADINGRATE_BUFFER_SIZE;

    if(fabs(lp_Navi->d_HeadingRate_Mean) >= THRESHOLD_HEADING_RATE)
    {
        lp_Navi->isHeadingchange = YES;
    }
    else
    {
        lp_Navi->isHeadingchange = NO;
    }
    
    // 误差修正处理 (需要适配)
    if((lp_Navi->Correct_Count > 0) && (g_SysVar.WorkPhase != PHASE_INS_NAVI))
    {
        // ErrCorrect_1_Navi_Time(&g_Navi, &g_Kalman); // 需要实现此函数
    }    
    lp_Navi->Navi_Count++;//完成一次完整导航解算过程后，将导航计数+1
}

/*****************************函数说明*************************************/
/*函数名称：ComputeWien                                                   */
/*函数功能描述:计算地球自转角速率在导航坐标系下的三轴分量                 */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：纬度: r_Lati                                                  */
/*输出变量：角速率: r_Wien                                                */
/*备注：该函数输入变量为值传递、输出变量为地址传递                        */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeWien(LATI r_Lati, ANGRATE r_Wien[3])
{
    r_Wien[0] = 0.0;
    r_Wien[1] = WIE * cos(r_Lati);
    r_Wien[2] = WIE * sin(r_Lati);
}

/*****************************函数说明*************************************/
/*函数名称：ComputeRmRn                                                   */
/*函数功能描述:计算当地子午圈半径、卯酉圈半径及其倒数                     */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：纬度: r_Lati、高度：Height                                    */
/*输出变量：指针：子午圈半径：lp_Rm、卯酉圈半径lp_Rn                      */
/*输出变量：指针：子午圈半径倒数：lp_invRm、卯酉圈半径倒数lp_invRn         */
/*备注：该函数输入变量为值传递、输出变量为地址传递                        */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeRmRn(LATI r_Lati, HEIGHT Height, LEN *lp_Rm, LEN *lp_Rn, DPARA *lp_invRm, DPARA *lp_invRn)
{
    DPARA SinLati = sin(r_Lati);
    DPARA SinLatiSqure = SinLati * SinLati;
    DPARA F = (1.0/298.257223563); // WGS84扁率
    *lp_Rm = RE - (2 * RE * F) + (3 * RE * F * SinLatiSqure) + Height;
    *lp_Rn = RE + F * RE * SinLatiSqure + Height;
    *lp_invRm = 1 / *lp_Rm;
    *lp_invRn = 1 / *lp_Rn;
}

/*****************************函数说明*************************************/
/*函数名称：ComputeWenn                                                   */
/*函数功能描述:计算载体相对于地球的转移角速率在导航坐标系下的三轴分量     */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：纬度: r_Lati、速度：Vn、参数：invRm、参数：invRn              */
/*输出变量：角速率: r_Wenn                                                */
/*备注：该函数输入变量为值传递、输出变量为地址传递                        */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeWenn(LATI r_Lati, VEL const Vn[3], DPARA invRm, DPARA invRn, ANGRATE r_Wenn[3])
{
    r_Wenn[0] = Vn[2] * invRn;
    r_Wenn[1] = r_Wenn[0] * tan(r_Lati);
    r_Wenn[2] = -Vn[0] * invRm;
}

/*****************************函数说明*************************************/
/*函数名称：ComputeWnbb                                                   */
/*函数功能描述:计算载体坐标系相对于导航坐标系的旋转角速率的三轴分量       */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：角速率: r_Wibb、角速率:r_Wien、角速率：r_Wenn、姿态矩阵：Cnb  */
/*输出变量：角速率: r_Wnbb                                                */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeWnbb(ANGRATE r_Wibb[2][3], ANGRATE const r_Wien[3], ANGRATE const r_Wenn[3], MATR Cnb[9], ANGRATE r_Wnbb[2][3])
{
    register IPARA i = 0, j = 0;
    ANGRATE r_Winn[3], r_Winb[3];

    for (i = 0; i < 3; i++)
    {
        r_Winn[i] = r_Wien[i] + r_Wenn[i];
    }

    Mat_Mul(Cnb, r_Winn, r_Winb, 3, 3, 1);
    for(j = 0; j < 2; j++)
    {
        for (i = 0; i < 3; i++)
        {
            r_Wnbb[j][i] = r_Wibb[j][i] - r_Winb[i];
        }
    }
}

/*****************************函数说明*************************************/
/*函数名称：ComputeDelSenbb                                               */
/*函数功能描述:计算角增量                                                 */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：角速率: r_Wnbb                                                */
/*输出变量：角增量: r_DelSenbb                                            */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeDelSenbb(ANGRATE r_Wnbb[2][3], DELANG r_DelSenbb_1[3], DELANG r_DelSenbb_2[3], DELANG r_DelSenbb[3])
{
    register IPARA i;
    DELANG r_DelSenbb_Compen[3] = { 0.0 };

    for (i = 0; i < 3; i++)
    {
        r_DelSenbb_1[i] = r_Wnbb[0][i] * TIME_NAVI;
    }
    for (i = 0; i < 3; i++)
    {
        r_DelSenbb_2[i] = r_Wnbb[1][i] * TIME_NAVI;
    }
    Vec_Cross(r_DelSenbb_1, r_DelSenbb_2, r_DelSenbb_Compen);

    for (i = 0; i < 3; i++)
    {
        r_DelSenbb[i] = r_DelSenbb_2[i] + 1.0 / 12.0 * r_DelSenbb_Compen[i];
    }
}

/*****************************函数说明*************************************/
/*函数名称：ComputeQ                                                      */
/*函数功能描述:更新计算载体姿态四元数                                     */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：角增量: r_DelSenbb                                            */
/*输出变量：惯导姿态四元数: Q                                             */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void ComputeQ(DELANG r_DelSenbb[3], QUAT Q[4])
{
    register IPARA i;
    register DPARA C;
    register DPARA S;
    register DPARA InvQSquRoot = 0.0;

    QUAT Qc[4];
    QUAT LastQ[4];
    DELANG r_Delta;

    r_Delta = sqrt(r_DelSenbb[0] * r_DelSenbb[0] + r_DelSenbb[1] * r_DelSenbb[1] + r_DelSenbb[2] * r_DelSenbb[2]);

    C = cos(0.5 * r_Delta);
    S = (fabs(r_Delta) >= MIN_DATA) ? sin(0.5 * r_Delta) / r_Delta : 0.5;//除零保护

    Qc[0] = C;
    Qc[1] = S * r_DelSenbb[0];
    Qc[2] = S * r_DelSenbb[1];
    Qc[3] = S * r_DelSenbb[2];

    //更新四元数
    for (i = 0; i < 4; i++)
    {
        LastQ[i] = Q[i];
    }
    Qua_Mul(LastQ, Qc, Q);

    //四元数归一化
    InvQSquRoot = 1.0 / sqrt(Q[0] * Q[0] + Q[1] * Q[1] + Q[2] * Q[2] + Q[3] * Q[3]);
    Q[0] = Q[0] * InvQSquRoot;
    Q[1] = Q[1] * InvQSquRoot;
    Q[2] = Q[2] * InvQSquRoot;
    Q[3] = Q[3] * InvQSquRoot;
}

/*****************************函数说明*************************************/
/*函数名称：QToCnb                                                        */
/*函数功能描述：完成惯导姿态四元数到姿态矩阵的转换                        */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：惯导姿态四元数:Q                                              */
/*输出变量：惯导姿态矩阵:Cnb                                              */
/*备注1：该函数输入变量、输出变量均为地址传递                             */
/*备注2：该函数输入姿态四元数应为归一化四元数                             */
/*返回值：无                                                              */
/**************************************************************************/
void QToCnb(QUAT const Q[4], MATR Cnb[9])
{
    Cnb[0] = (Q[0] * Q[0]) + (Q[1] * Q[1]) - (Q[2] * Q[2]) - (Q[3] * Q[3]);
    Cnb[1] = 2 * (Q[1] * Q[2] + Q[0] * Q[3]);
    Cnb[2] = 2 * (Q[1] * Q[3] - Q[0] * Q[2]);

    Cnb[3] = 2 * (Q[1] * Q[2] - Q[0] * Q[3]);
    Cnb[4] = (Q[0] * Q[0]) - (Q[1] * Q[1]) + (Q[2] * Q[2]) - (Q[3] * Q[3]);
    Cnb[5] = 2 * (Q[2] * Q[3] + Q[0] * Q[1]);

    Cnb[6] = 2 * (Q[1] * Q[3] + Q[0] * Q[2]);
    Cnb[7] = 2 * (Q[2] * Q[3] - Q[0] * Q[1]);
    Cnb[8] = (Q[0] * Q[0]) - (Q[1] * Q[1]) - (Q[2] * Q[2]) + (Q[3] * Q[3]);
}

/*****************************函数说明*************************************/
/*函数名称：CnbToQ                                                        */
/*函数功能描述：完成惯导姿态矩阵到姿态四元数的转换                        */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：惯导姿态矩阵:Cnb                                              */
/*输出变量：惯导姿态四元数:Q                                              */
/*备注：该函数输入变量、输出变量均为地址传递                              */
/*返回值：无                                                              */
/**************************************************************************/
void CnbToQ(MATR Cnb[9], QUAT Q[4])
{
    IPARA i;
    //检查Cnb中是否存在极小元素，并置为0，避免产生sqrt对极小绝对值的负数开方产生的溢出
    for(i = 0; i < 9; i++)
    {
        if(fabs(Cnb[i]) <= MIN_DATA)
        {
            Cnb[i] = 0.0;
        }
    }
    Q[0] = 0.5 * sqrt(1 + Cnb[0] + Cnb[4] + Cnb[8]);

    Q[1] = (Cnb[5] >= Cnb[7]) ? (0.5 * sqrt(1 + Cnb[0] - Cnb[4] - Cnb[8])) : (-0.5 * sqrt(1 + Cnb[0] - Cnb[4] - Cnb[8]));

    Q[2] = (Cnb[6] >= Cnb[2]) ? (0.5 * sqrt(1 - Cnb[0] + Cnb[4] - Cnb[8])) : (-0.5 * sqrt(1 - Cnb[0] + Cnb[4] - Cnb[8]));

    Q[3] = (Cnb[1] >= Cnb[3]) ? (0.5 * sqrt(1 - Cnb[0] - Cnb[4] + Cnb[8])) : (-0.5 * sqrt(1 - Cnb[0] - Cnb[4] + Cnb[8]));
}
