/***********************************************************************************
nav application module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|DengWei       |  2023-2-13         | Modify                     |
 Add EPSON_G370 sensor
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"
#include <string.h>
#include <stdio.h>
#include "gnss.h"
// HPM6750算法集成
#include "hpm6750_algorithm/include/hpm6750_types.h"
#include "hpm6750_algorithm/include/hpm6750_datastruct.h"
#include "hpm6750_algorithm/include/hpm6750_functions.h"
//#ifndef linux
//#include "UartAdapter.h"
//#endif
//int timecount = 0;
//?????? 
//NAV_Data_t NAV_Data;  

//Sensors_Data_t Sensors_Data = {0};
//Param_Data_t Param_Data = {0};
//Param_t Param_Data_temp = {0};
//CombineDataTypeDef *Sensors = &combineData; //?????????????
char g_ALGODebugInfo[1024]={0};
unsigned long int g_NavIndex=0;

#if 0
//???????????
double P_gyro_Cvn=(100.0)*(100.0);
double Q_gyro_Cvn =(0.001f)*(0.001f)*0.005;
double R_gyro_Cvn =(1.0f)*(1.0f);

//??????????
double P_acc_Cvn=(20.0)*(20.0);
double Q_acc_Cvn =(0.001f)*(0.001f)*0.005;
double R_acc_Cvn =(1.0f)*(1.0f);
#endif

///////////////////////////////////////////////////////////////////////////////////

//???????????????
_NAV_Data_Full_t NAV_Data_Full = {0};	 //??????????
_NAV_Data_Out_t NAV_Data_Out = {0};      //???????
_NAV_Funsion_Status_Time_t g_NAV_Funsion_Status_Time={0};//??????????????




//??????????Χ??(-180 180]
double CorrHeading(double orginheading)
{
	if(orginheading<=-180)
	{
		return orginheading+360;
	}
	else if(orginheading>180)
	{
		return orginheading-360;
	}
	else
	{
		return orginheading;
	}
}
//****Guolong Zhang*****[-pi~pi]*****
double CorrHeading_PI(double orginheading)//*****?????rad***
{
	if(orginheading<=-PI)
	{
		return orginheading+2*PI;
	}
	else if(orginheading>PI)
	{
		return orginheading-2*PI;
	}
	else
	{
		return orginheading;
	}
}



/******************************************************************************
*?  ???void KinematicEstimation(_NAV_Data_Full_t* NAV_Data_Full_p)
*??  ?????????????
*??  ????
*??  ??????
*******************************************************************************/
void KinematicEstimation(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#if 0
	//??????????
	//????1:???
	//???????:???????????0?? ?????????м?????????
	if(		NAV_Data_Full_p->SINS.vn[0]<MAX_STATIC_VN 
		&& 	NAV_Data_Full_p->SINS.vn[1]<MAX_STATIC_VN 
		&&   )
#endif		
	
}

/******************************************************************************
*?  ???void Param_Data_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
*??  ????????????
*??  ????
*??  ??????
*******************************************************************************/
void Param_Data_Init(_NAV_Data_Full_t* NAV_Data_Full_p)
{
#ifndef WIN32
	combineData.Param.methodType =	E_METHOD_TYPE_KALMAN;
	combineData.Param.sim = E_SIM_MODE_NORMAL;
#endif
	NAV_Data_Full_p->KF.use_gps_flag 		=	E_FUNSION_GPS;	
	NAV_Data_Full_p->Gnss_Use_Status		=	E_NAV_SUPPORT_RTK_ALL_STATUS;//E_NAV_SUPPORT_RTK_FIEX_STATUS;//E_NAV_SUPPORT_RTK_ALL_STATUS;//E_NAV_SUPPORT_RTK_FIEX_STATUS
		
	NAV_Data_Full_p->ODS.ods_flag 			= E_ODS_WHEEL_FLAG_NONE;//;E_ODS_WHEEL_FLAG_NONE;E_ODS_WHEEL_FLAG_HAVE
	NAV_Data_Full_p->ODS.ods_caculat_flag	= RETURN_FAIL;//******

	//if(E_IMU_MANU_460 == NAV_Data_Full_p->memsType)
	//{
	//	NAV_Data_Full_p->KF.measure_flag_head	=	E_KALMAN_MEASURE_HEADING_YES;//E_KALMAN_MEASURE_HEADING_NO
	//}
	//else if(E_IMU_MANU_ADIS16465 == NAV_Data_Full_p->memsType)
	//{
	//	NAV_Data_Full_p->KF.measure_flag_head =	E_KALMAN_MEASURE_HEADING_YES;
	//}
	//else
	//{
	//	NAV_Data_Full_p->KF.measure_flag_head	= E_KALMAN_MEASURE_HEADING_NO;
	//}
	
	NAV_Data_Full_p->KF.measure_flag_head = E_KALMAN_MEASURE_HEADING_NO;
	NAV_Data_Full_p->KF.measure_flag_Wheel = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_ZUPT = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_NHC = RETURN_FAIL;
	NAV_Data_Full_p->KF.measure_flag_vn = E_KALMAN_MEASURE_VEL_NO;
	NAV_Data_Full_p->KF.measure_flag_pos = E_KALMAN_MEASURE_POS_NO;
	NAV_Data_Full_p->KF.measure_flag = E_KALMAN_MEASURE_UPDATE_NONE;
}

void NavDRMode(unsigned int testcount)
{

	if(	   (NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
		&& NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
		&& E_SIM_MODE_NORMAL == combineData.Param.sim
	   )
	{
		
//		SINS_UP(&NAV_Data_Full);
//		KF_UP2(&NAV_Data_Full,&combineData);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
	else if(		(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) //'A'
				&& NAV_Data_Full.GPS.rtkStatus == E_GPS_RTK_FIXED
				&& E_SIM_MODE_DEBUG == combineData.Param.sim
				&& testcount<combineData.Param.lostepoch
	   		)
	{
		//SINS_UP(&NAV_Data_Full);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
	else
	{
//		SetNavFunsionSource(E_FUNSION_WHEEL);
//		SINS_UP_DR(&NAV_Data_Full);
//		//???????????????????
//		TEST_UP(&NAV_Data_Full);
		SINS_Update(&NAV_Data_Full);
		KF_UP2(&NAV_Data_Full,&combineData);
	}
}

void NavKalmanMode(unsigned int testcount)
{
	#if 0
	if(0 == combineData.Param.HP)
	{
		SINS_UP(&NAV_Data_Full);
	}
	else
	{
		//SINS_UP_HP(&NAV_Data_Full);
		SINS_UP(&NAV_Data_Full);
	}
	#endif
	SINS_Update(&NAV_Data_Full);
	//if(E_SIM_MODE_DEBUG == combineData.Param.sim)
	//{
	//	if(testcount>=combineData.Param.lostepoch)
	//	{
	//		//NAV_Data_Full.KF.use_gps_flag = E_FUNSION_WHEEL;//E_FUNSION_WHEEL;//E_FUNSION_MOTION;
	//		//hGPSData.rtkStatus = 0 ;
	//		NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
	//	}		
	//}
	KF_UP2(&NAV_Data_Full,&combineData);		
}

//????????
/******************************************************************************
*?  ???void NAV_function(void)
*??  ??????????????????????????????+ODS+GPS
*??  ????
*??  ??????
*******************************************************************************/
void NAV_function(void)//??????????
{
	static unsigned long row=0;
	row++;
	if(row%10000 ==0)
	printf("????????????%ld??\n",row);

	//static long int navindex=0;
	static unsigned long testcount=0;
	g_NavIndex++;

	//????????????? ?? ??????
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	Get_ODS_Data(&NAV_Data_Full,&combineData);

    //????????Kinematic estimation
	//KinematicEstimation(&NAV_Data_Full);
	//*******????GPS???****?????10min??????*****
	//int i = 0;
	//if(NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)
	//for (i = 0; i < outnum; i++)
	//{
	//	if ((NAV_Data_Full.GPS.gpssecond982 / 1000.0 - NAV_Data_Full.outage_start_per[i] > 0.0)
	//		&& (NAV_Data_Full.GPS.gpssecond982 / 1000.0 - NAV_Data_Full.outage_start_per[i] < outagetime))
	//	{
	//		//NAV_Data_Full.GPS.gps_up_flag = E_GPS_NO_UPDATE;
	//		//NAV_Data_Full.GPS.rtkStatus = E_GPS_RTK_INVALID;
	//		//hGPSData.rtkStatus = E_GPS_RTK_INVALID;//*******??????λ?????0****
	//		//NAV_Data_Full.GPS.Position_Status = E_GPS_POS_INVALID;
	//	}
	//}
	//testcount++;
	switch(NAV_Data_Full.Nav_Status)
	{
	    case E_NAV_STATUS_START://??????? ???????????0
		{
			//???????ò???
			Param_Data_Init(&NAV_Data_Full);			
			//?????????,?????????????ж?????????????
			Load_Standard_Data(&NAV_Data_Full,&combineData);//**************
			SetNavStatus(E_NAV_STATUS_ROUTH_ALIGN);
		    //NAV_Data_Full.Nav_Status = E_NAV_STATUS_ROUTH_ALIGN;  
		}
		break;

		case E_NAV_STATUS_ROUTH_ALIGN://SINS??????1
		{
			static int GPS_lastnum = 0;
			
			if(	//(NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID || NAV_Data_Full.GPS.Position_Status	== E_GPS_POS_VALID_RTK) &&
			     (NAV_Data_Full.GPS.Position_Status != E_GPS_POS_INVALID) &&
			     (NAV_Data_Full.GPS.gps_up_flag == E_GPS_IS_UPDATE)
				&& NAV_Data_Full.GPS.rtkStatus>0
				//&&(NAV_Data_Full.GPS.headingStatus	== E_GPS_RTK_FIXED)	
			  )						
			{	
				//SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
				GPS_lastnum++;
				if (GPS_lastnum > 25)
				{
					SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
				}
				
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_SINS_KF_INITIAL;// NAV_INS_STATUS_ROUTH_ALIGN;
			}			
		}
		break;

		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF?????2
		{
			
 			SINS_Init(&NAV_Data_Full);			
			if(NAV_Data_Full.SINS.Init_flag == 1)
			{
				
				SetNavStatus(E_NAV_STATUS_IN_NAV);
				//if(NAV_Data_Full.Nav_Standard_flag ==E_NAV_STANDARD_PROCCSSED)
				//{
				//	SetNavStatus(E_NAV_STATUS_IN_NAV);
				//	//g_KF_UP2_gps_delay_cunt = 0;//????л??????????????????0
				//	//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
				//}
				//else
				//{
				//	SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
				//	//g_KF_UP2_gps_delay_cunt = 0;//????л???????????????0
				//	//NAV_Data_Full.Nav_Status = E_NAV_STATUS_SYSTEM_STANDARD;
				//}
				KF_Init(&NAV_Data_Full); 
			}

		}
		break;
		case E_NAV_STATUS_SYSTEM_STANDARD://????*****3
		{
			/*
			if(0 == combineData.Param.HP)
			{
				SINS_UP(&NAV_Data_Full);
			}
			else
			{
				//SINS_UP_HP(&NAV_Data_Full);
				SINS_UP(&NAV_Data_Full);
			}
			*/
			SINS_Update(&NAV_Data_Full);
			KF_UP2(&NAV_Data_Full,&combineData);
			
//			if (sqrt(NAV_Data_Full.GPS.ve*NAV_Data_Full.GPS.ve + NAV_Data_Full.GPS.vu*NAV_Data_Full.GPS.vu + NAV_Data_Full.GPS.vn*NAV_Data_Full.GPS.vn)>2
//				&&	NAV_Data_Full.GPS.rtkStatus 		== E_GPS_RTK_FIXED)
//			{
//				timecount ++ ;
//			}
//			else if (timecount >=1)
//			{
//				timecount ++;
//			}
#if 0
			if(g_NavIndex==100000)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
				//KF_Init(&NAV_Data_Full);
				//g_KF_UP2_gps_delay_cunt = 0;//????л??????????????????0
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
			}
#endif			
			if(NAV_Data_Full.Nav_Standard_flag ==E_NAV_STANDARD_PROCCSSED)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
				//KF_Init(&NAV_Data_Full);
				//g_KF_UP2_gps_delay_cunt = 0;//????л??????????????????0
				//NAV_Data_Full.Nav_Status = E_NAV_STATUS_IN_NAV;
				//?????????
				//Save_Standard_Data(&NAV_Data_Full,&combineData);
#ifdef WIN32
				inav_log(INAVMD(LOG_DEBUG),"gnssAtt_from_vehicle2=%f",NAV_Data_Full.Param.gnssAtt_from_vehicle2[2]);
#endif				
			}
		}
		break;
		case E_NAV_STATUS_IN_NAV://???????? *****4
		{	
			++testcount;		
//			SINS_Update(&NAV_Data_Full);
//		  KF_UP2(&NAV_Data_Full,&combineData);
			//NavKalmanMode(g_NavIndex);
#if 1
			if(E_METHOD_TYPE_KALMAN == combineData.Param.methodType)
			{
				NavKalmanMode(g_NavIndex);//
			}
			/*
			else if(E_METHOD_TYPE_DR == combineData.Param.methodType)
			{
				NavDRMode(g_NavIndex);
			}
			*/
			else
			{
#ifdef WIN32
				inav_log(INAVMD(LOG_ERR),"methodType=%d",combineData.Param.methodType);
#endif		
			}
#endif
		}
		break;

		case E_NAV_STATUS_STOP://??????
		{
 			//StopNavigation();
		}
		break;
		
		default: //????????
		{
			break;	
		}
	}

	#ifdef WIN32	
	//???????????????????Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	//if(NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)
	Out_Data_Up(&NAV_Data_Out); //???????????NAV_Data_Out


#if 0	
	if(20000==g_NavIndex)
	{
		SendOBSIMUData(&NAV_Data_Out,&NAV_Data_Full);
		SendOBSGNSSData(&NAV_Data_Out,&NAV_Data_Full);
		SendSINSData(&NAV_Data_Out,&NAV_Data_Full);
	}
#endif
}

//????????
/******************************************************************************
*?  ???void NAV_function_LD_TEST(void)
*??  ???????????????????
*??  ????
*??  ??????
*******************************************************************************/
void NAV_function_LD_TEST(void)//??????????
{
	//????????????????????
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	Get_GNSS_Data(&NAV_Data_Full,&combineData);
	NAV_Data_Full.GPS.Heading = 0;
	Get_ODS_Data(&NAV_Data_Full,&combineData);
	
	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://??????? ???????????
		{
			//???????ò???
			Param_Data_Init(&NAV_Data_Full);
			//?????????,?????????????ж?????????????
			Load_Standard_Data(&NAV_Data_Full,&combineData);
			SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
		}
		break;	
		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF?????
		{
			NAV_Data_Full.SINS.pos[0]	=	22.74418555666*DEG2RAD;//pNAV_GNSS_RESULT->latitude*DEG2RAD;//
			NAV_Data_Full.SINS.pos[1]	=	113.8147628866*DEG2RAD;//pNAV_GNSS_RESULT->longitude * DEG2RAD;//
			NAV_Data_Full.SINS.pos[2]	=	0;//pNAV_GNSS_RESULT->altitude ;//????λm 
			NAV_Data_Full.SINS.vn[0] = 0;
			NAV_Data_Full.SINS.vn[1] = 0;
			NAV_Data_Full.SINS.vn[2] = 0;
 			SINS_Init(&NAV_Data_Full);			
			SetNavStatus(E_NAV_STATUS_SYSTEM_STANDARD);
			
		}
		break;
		case E_NAV_STATUS_SYSTEM_STANDARD://????
		{
				Earth_UP(&NAV_Data_Full,NAV_Data_Full.SINS.pos,NAV_Data_Full.SINS.vn);
				MahonyUpdate_NoMAG(&NAV_Data_Full);	
		}
		break;
	}
#ifdef WIN32	
	//???????????????????Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	Out_Data_Up(&NAV_Data_Out); //??????????

}


//????????
/******************************************************************************
*?  ???void NAV_function_UAV(void)
*??  ???????????????????????????????
*??  ????
*??  ??????
*******************************************************************************/
void NAV_function_UAV(void)//??????????
{
	g_NavIndex++;
	//????????????? ?? ??????
	Get_Param_Data(&NAV_Data_Full,&combineData);
	Get_IMU_Data(&NAV_Data_Full,&combineData);
	
	

	switch(NAV_Data_Full.Nav_Status)
	{
		case E_NAV_STATUS_START://??????? ???????????
		{
			//???????ò???
			Param_Data_Init(&NAV_Data_Full);
			//?????????,?????????????ж?????????????
			Load_Standard_Data(&NAV_Data_Full,&combineData);
			SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
		    //NAV_Data_Full.Nav_Status = E_NAV_STATUS_ROUTH_ALIGN;  
		}
		break;

		case E_NAV_STATUS_SINS_KF_INITIAL://SINS KF?????
		{
 			SINS_Init(&NAV_Data_Full);			
			if(NAV_Data_Full.SINS.Init_flag == 1)
			{
				SetNavStatus(E_NAV_STATUS_IN_NAV);
			}
		}
		break;

		case E_NAV_STATUS_IN_NAV://???????? 
		{
			MahonyUpdate(&NAV_Data_Full);	
		}
		break;
		case E_NAV_STATUS_STOP://??????
		{
 			//StopNavigation();
		}
		break;
		default: //????????
		{
			break;	
		}
	}
#ifdef WIN32	
	//???????????????????Nav_Status,Nav_Standard_flag
	PrintOutStateChange(g_NavIndex,&NAV_Data_Full);
	//NMEA out
#endif
	//???ASCIIЭ??????
	Out_Data_Up(&NAV_Data_Out); //??????????
}




/*****************************函数说明*************************************/
/*函数名称：NAV_function_HPM6750                                          */
/*函数功能描述：使用HPM6750算法的导航处理函数                             */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：无                                                            */
/*输出变量：无                                                            */
/*返回值：无                                                              */
/**************************************************************************/
void NAV_function_HPM6750(void)
{
    static unsigned char hpm6750_initialized = NO;

    g_NavIndex++;

    // 获取传感器数据和系统参数
    Get_Param_Data(&NAV_Data_Full, &combineData);
    Get_IMU_Data(&NAV_Data_Full, &combineData);
    Get_GNSS_Data(&NAV_Data_Full, &combineData);
    Get_ODS_Data(&NAV_Data_Full, &combineData);

    // HPM6750算法初始化
    if (hpm6750_initialized == NO)
    {
        HPM6750_Algorithm_Init();
        hpm6750_initialized = YES;
    }

    switch(NAV_Data_Full.Nav_Status)
    {
        case E_NAV_STATUS_START: // 开始状态 加载参数配置
        {
            // 参数配置初始化
            Param_Data_Init(&NAV_Data_Full);
            // 加载标定数据
            Load_Standard_Data(&NAV_Data_Full, &combineData);
            SetNavStatus(E_NAV_STATUS_SINS_KF_INITIAL);
        }
        break;

        case E_NAV_STATUS_SINS_KF_INITIAL: // SINS KF初始化
        {
            // 使用HPM6750算法进行处理
            HPM6750_Algorithm_Process(&NAV_Data_Full.IMU, &NAV_Data_Full.GPS, &NAV_Data_Full.SINS);

            // 检查对准是否完成
            if (g_SysVar.WorkPhase >= PHASE_INS_NAVI)
            {
                NAV_Data_Full.SINS.Init_flag = 1;
                NAV_Data_Full.SINS.Align_flag = 2; // 对准完成
                SetNavStatus(E_NAV_STATUS_IN_NAV);
            }
            else
            {
                NAV_Data_Full.SINS.Align_flag = 1; // 对准中
            }
        }
        break;

        case E_NAV_STATUS_IN_NAV: // 导航运行
        {
            // 使用HPM6750算法进行导航解算
            HPM6750_Algorithm_Process(&NAV_Data_Full.IMU, &NAV_Data_Full.GPS, &NAV_Data_Full.SINS);

            // 更新导航状态
            Update_NAV_Status_From_HPM6750(&NAV_Data_Full);
        }
        break;

        case E_NAV_STATUS_STOP: // 停止导航
        {
            // 停止导航处理
        }
        break;

        default: // 其他异常状态
        {
            break;
        }
    }

#ifdef WIN32
    // 打印当前单元的各种状态迁移Nav_Status,Nav_Standard_flag
    PrintOutStateChange(g_NavIndex, &NAV_Data_Full);

    // 打印HPM6750算法状态信息
    char hpm6750_status[256];
    Get_HPM6750_Algorithm_Status(hmp6750_status, sizeof(hpm6750_status));
    printf("HPM6750: %s\n", hpm6750_status);
#endif

    // 输出数据更新
    Out_Data_Up(&NAV_Data_Out);
}

////////////////////////end/////////////////////////////////////////////////////
