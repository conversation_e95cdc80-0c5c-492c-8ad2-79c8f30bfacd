/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_test.c - HPM6750算法移植测试程序                                                                                        */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：用于测试HPM6750算法移植的正确性                                                                                                */
/*******************************************************************************************************************************************/

#include "include/hpm6750_types.h"
#include "include/hpm6750_datastruct.h"
#include "include/hpm6750_functions.h"
#include "../nav_type.h"

/*****************************函数说明*************************************/
/*函数名称：HPM6750_Algorithm_Test                                        */
/*函数功能描述：HPM6750算法移植测试函数                                   */
/*版本号：  Ver 1.0                                                       */
/*移植日期：2025-01-18                                                     */
/*输入变量：无                                                            */
/*输出变量：测试结果                                                      */
/*返回值：0-成功，非0-失败                                                */
/**************************************************************************/
int HPM6750_Algorithm_Test(void)
{
    // 创建测试数据
    _IMU_t test_imu = {0};
    _GPS_UP_DAT test_gps = {0};
    _NAV_t test_nav_out = {0};
    
    printf("开始HPM6750算法移植测试...\n");
    
    // 初始化测试IMU数据
    test_imu.gyro_use[0] = 0.001;  // X轴陀螺 (rad/s)
    test_imu.gyro_use[1] = 0.001;  // Y轴陀螺 (rad/s)
    test_imu.gyro_use[2] = 0.001;  // Z轴陀螺 (rad/s)
    
    test_imu.acc_use[0] = 0.1;     // X轴加速度计 (m/s^2)
    test_imu.acc_use[1] = -9.8;    // Y轴加速度计 (m/s^2)
    test_imu.acc_use[2] = 0.1;     // Z轴加速度计 (m/s^2)
    
    // 初始化测试GPS数据
    test_gps.Lat = 39.9042;        // 北京纬度 (度)
    test_gps.Lon = 116.4074;       // 北京经度 (度)
    test_gps.Altitude = 50.0;      // 高度 (m)
    test_gps.vn = 0.0;             // 北向速度 (m/s)
    test_gps.vu = 0.0;             // 天向速度 (m/s)
    test_gps.ve = 0.0;             // 东向速度 (m/s)
    test_gps.Heading = 0.0;        // 航向 (度)
    test_gps.Position_Status = 4;   // RTK固定解
    test_gps.rtkStatus = 4;
    test_gps.Sate_Num = 12;
    
    // 初始化HPM6750算法
    printf("初始化HPM6750算法...\n");
    HPM6750_Algorithm_Init();
    
    // 模拟对准过程 (180秒)
    printf("开始对准过程...\n");
    for (int i = 0; i < 36000; i++) // 180秒 * 200Hz = 36000次
    {
        // 更新前一时刻数据
        for (int j = 0; j < 3; j++)
        {
            test_imu.gyro_use_pre[j] = test_imu.gyro_use[j];
            test_imu.acc_use_pre[j] = test_imu.acc_use[j];
        }
        
        // 添加小的随机扰动模拟真实传感器数据
        test_imu.gyro_use[0] = 0.001 + (rand() % 100 - 50) * 1e-6;
        test_imu.gyro_use[1] = 0.001 + (rand() % 100 - 50) * 1e-6;
        test_imu.gyro_use[2] = 0.001 + (rand() % 100 - 50) * 1e-6;
        
        test_imu.acc_use[0] = 0.1 + (rand() % 100 - 50) * 1e-4;
        test_imu.acc_use[1] = -9.8 + (rand() % 100 - 50) * 1e-4;
        test_imu.acc_use[2] = 0.1 + (rand() % 100 - 50) * 1e-4;
        
        // 调用HPM6750算法
        HPM6750_Algorithm_Process(&test_imu, &test_gps, &test_nav_out);
        
        // 每10秒打印一次状态
        if (i % 2000 == 0)
        {
            printf("时间: %.1fs, 工作阶段: %d, 姿态角: [%.3f, %.3f, %.3f] 度\n",
                   i * 0.005,
                   g_SysVar.WorkPhase,
                   test_nav_out.att[0],
                   test_nav_out.att[1], 
                   test_nav_out.att[2]);
        }
        
        // 对准完成后退出
        if (g_SysVar.WorkPhase >= PHASE_INS_NAVI)
        {
            printf("对准完成，用时: %.1f秒\n", i * 0.005);
            break;
        }
    }
    
    // 模拟导航过程 (10秒)
    printf("开始导航过程...\n");
    for (int i = 0; i < 2000; i++) // 10秒 * 200Hz = 2000次
    {
        // 更新前一时刻数据
        for (int j = 0; j < 3; j++)
        {
            test_imu.gyro_use_pre[j] = test_imu.gyro_use[j];
            test_imu.acc_use_pre[j] = test_imu.acc_use[j];
        }
        
        // 模拟运动数据
        test_imu.gyro_use[0] = 0.001 + sin(i * 0.005) * 0.01; // 模拟俯仰运动
        test_imu.gyro_use[1] = 0.001 + cos(i * 0.005) * 0.01; // 模拟横滚运动
        test_imu.gyro_use[2] = 0.001 + sin(i * 0.001) * 0.05; // 模拟航向运动
        
        test_imu.acc_use[0] = 0.1 + sin(i * 0.005) * 0.5;
        test_imu.acc_use[1] = -9.8 + cos(i * 0.005) * 0.2;
        test_imu.acc_use[2] = 0.1 + sin(i * 0.003) * 0.3;
        
        // 调用HPM6750算法
        HPM6750_Algorithm_Process(&test_imu, &test_gps, &test_nav_out);
        
        // 每2秒打印一次结果
        if (i % 400 == 0)
        {
            printf("导航时间: %.1fs, 位置: [%.6f, %.6f, %.1f], 姿态: [%.3f, %.3f, %.3f]\n",
                   i * 0.005,
                   test_nav_out.pos[0], test_nav_out.pos[1], test_nav_out.pos[2],
                   test_nav_out.att[0], test_nav_out.att[1], test_nav_out.att[2]);
        }
    }
    
    printf("HPM6750算法移植测试完成！\n");
    printf("最终结果:\n");
    printf("  位置: 纬度=%.6f°, 经度=%.6f°, 高度=%.1fm\n", 
           test_nav_out.pos[0], test_nav_out.pos[1], test_nav_out.pos[2]);
    printf("  速度: 北向=%.3fm/s, 天向=%.3fm/s, 东向=%.3fm/s\n",
           test_nav_out.vn[0], test_nav_out.vn[1], test_nav_out.vn[2]);
    printf("  姿态: 俯仰=%.3f°, 横滚=%.3f°, 航向=%.3f°\n",
           test_nav_out.att[0], test_nav_out.att[1], test_nav_out.att[2]);
    
    return 0;
}

////////////////////////end/////////////////////////////////////////////////////
