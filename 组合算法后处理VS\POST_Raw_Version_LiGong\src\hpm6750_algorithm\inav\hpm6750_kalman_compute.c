/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_kalman_compute.c - HPM6750卡尔曼滤波计算函数                                                                           */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：从HPM6750项目移植卡尔曼滤波相关的计算函数                                                                                       */
/*******************************************************************************************************************************************/

#include "../include/hpm6750_types.h"
#include "../include/hpm6750_datastruct.h"
#include "../include/hpm6750_functions.h"

/*********************************函数说明***************************************/
/*函数名称：ComputeFn                                                            */
/*函数功能描述：计算状态转移矩阵Fn                                               */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：导航结构体指针lp_Navi                                                */
/*输出变量：状态转移矩阵Fn                                                       */
/*返回值：无                                                                     */
/*********************************************************************************/
void ComputeFn(p_Navi const lp_Navi, MATR Fn[DIM_STATE * DIM_STATE])
{
    IPARA i, j;
    
    // 初始化为零矩阵
    for (i = 0; i < DIM_STATE * DIM_STATE; i++)
    {
        Fn[i] = 0.0;
    }
    
    // 简化的状态转移矩阵 (15维状态向量)
    // 位置误差方程
    Fn[0 * DIM_STATE + 3] = 1.0;  // δLat = δVn / Rm
    Fn[1 * DIM_STATE + 4] = 1.0;  // δh = -δVu
    Fn[2 * DIM_STATE + 5] = 1.0;  // δLon = δVe / (Rn*cos(Lat))
    
    // 速度误差方程
    Fn[3 * DIM_STATE + 6] = lp_Navi->Gn;  // δVn与俯仰角误差耦合
    Fn[4 * DIM_STATE + 7] = -lp_Navi->Gn; // δVu与横滚角误差耦合
    Fn[5 * DIM_STATE + 8] = 0.0;          // δVe与航向角误差耦合
    
    // 姿态误差方程
    Fn[6 * DIM_STATE + 9] = 1.0;   // 俯仰角误差与X轴陀螺偏差
    Fn[7 * DIM_STATE + 10] = 1.0;  // 横滚角误差与Y轴陀螺偏差
    Fn[8 * DIM_STATE + 11] = 1.0;  // 航向角误差与Z轴陀螺偏差
    
    // 陀螺偏差和加速度计偏差为随机游走模型，对角线为0
}

/*********************************函数说明***************************************/
/*函数名称：ComputeFk                                                            */
/*函数功能描述：计算离散状态转移矩阵Fk                                           */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：连续状态转移矩阵Fn                                                   */
/*输出变量：离散状态转移矩阵Fk                                                   */
/*返回值：无                                                                     */
/*********************************************************************************/
void ComputeFk(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE])
{
    IPARA i;
    
    // 简化实现：Fk = I + Fn * dt
    // 初始化为单位矩阵
    for (i = 0; i < DIM_STATE * DIM_STATE; i++)
    {
        Fk[i] = 0.0;
    }
    for (i = 0; i < DIM_STATE; i++)
    {
        Fk[i * DIM_STATE + i] = 1.0;
    }
    
    // 加上 Fn * dt
    for (i = 0; i < DIM_STATE * DIM_STATE; i++)
    {
        Fk[i] += Fn[i] * TIME_FILTER;
    }
}

/*********************************函数说明***************************************/
/*函数名称：ComputeXkk_1                                                         */
/*函数功能描述：计算状态预测向量                                                 */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：状态转移矩阵Fk，状态向量Xk                                          */
/*输出变量：状态预测向量Xkk_1                                                   */
/*返回值：无                                                                     */
/*********************************************************************************/
void ComputeXkk_1(MATR const Fk[DIM_STATE * DIM_STATE], VEC const Xk[DIM_STATE], VEC Xkk_1[DIM_STATE])
{
    Mat_Mul(Fk, Xk, Xkk_1, DIM_STATE, DIM_STATE, 1);
}

/*********************************函数说明***************************************/
/*函数名称：ComputePkk_1                                                         */
/*函数功能描述：计算协方差预测矩阵                                               */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：状态转移矩阵Fk，协方差矩阵Pk，过程噪声矩阵Qk                       */
/*输出变量：协方差预测矩阵Pkk_1                                                 */
/*返回值：无                                                                     */
/*********************************************************************************/
void ComputePkk_1(MATR Fk[DIM_STATE * DIM_STATE], MATR Pk[DIM_STATE * DIM_STATE], 
                  MATR Qk[DIM_STATE * DIM_STATE], MATR Pkk_1[DIM_STATE * DIM_STATE])
{
    MATR FkT[DIM_STATE * DIM_STATE];
    MATR temp[DIM_STATE * DIM_STATE];
    
    // 计算Fk的转置
    Mat_Tr(Fk, FkT, DIM_STATE, DIM_STATE);
    
    // 计算Fk * Pk
    Mat_Mul(Fk, Pk, temp, DIM_STATE, DIM_STATE, DIM_STATE);
    
    // 计算Fk * Pk * Fk^T
    Mat_Mul(temp, FkT, Pkk_1, DIM_STATE, DIM_STATE, DIM_STATE);
    
    // 加上过程噪声矩阵Qk
    Mat_Add(Pkk_1, Qk, Pkk_1, DIM_STATE, DIM_STATE);
}

/*********************************函数说明***************************************/
/*函数名称：ComputeZk                                                            */
/*函数功能描述：计算观测向量                                                     */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：GNSS数据指针，卡尔曼滤波结构体指针，观测拟合模式                     */
/*输出变量：观测向量Zk                                                           */
/*返回值：无                                                                     */
/*********************************************************************************/
void ComputeZk(p_GNSSData const lp_GNSSData, p_Kalman lp_Kalman, MODE Obv_Fit_Mode)
{
    extern Navi g_Navi;
    
    switch(Obv_Fit_Mode)
    {
        case POSVEL_FIT: // 位置速度观测
            // 位置观测残差
            lp_Kalman->Zk[0] = (lp_GNSSData->Lati - g_Navi.r_Lati) * g_Navi.Rm;  // 纬度残差转换为米
            lp_Kalman->Zk[1] = lp_GNSSData->Height - g_Navi.Height;               // 高度残差
            lp_Kalman->Zk[2] = (lp_GNSSData->Logi - g_Navi.r_Logi) * g_Navi.Rn * cos(g_Navi.r_Lati); // 经度残差转换为米
            // 速度观测残差
            lp_Kalman->Zk[3] = lp_GNSSData->Vn[0] - g_Navi.Vn[0]; // 北向速度残差
            lp_Kalman->Zk[4] = lp_GNSSData->Vn[1] - g_Navi.Vn[1]; // 天向速度残差
            lp_Kalman->Zk[5] = lp_GNSSData->Vn[2] - g_Navi.Vn[2]; // 东向速度残差
            break;
            
        case POSVELHEAD_FIT: // 位置速度航向观测
            // 位置速度观测残差 (同上)
            lp_Kalman->Zk[0] = (lp_GNSSData->Lati - g_Navi.r_Lati) * g_Navi.Rm;
            lp_Kalman->Zk[1] = lp_GNSSData->Height - g_Navi.Height;
            lp_Kalman->Zk[2] = (lp_GNSSData->Logi - g_Navi.r_Logi) * g_Navi.Rn * cos(g_Navi.r_Lati);
            lp_Kalman->Zk[3] = lp_GNSSData->Vn[0] - g_Navi.Vn[0];
            lp_Kalman->Zk[4] = lp_GNSSData->Vn[1] - g_Navi.Vn[1];
            lp_Kalman->Zk[5] = lp_GNSSData->Vn[2] - g_Navi.Vn[2];
            // 航向观测残差
            lp_Kalman->Zk[6] = lp_GNSSData->Heading - g_Navi.r_Atti[0]; // 航向残差
            break;
            
        case ZERO_SPEED: // 零速观测
            // 速度观测残差 (零速约束)
            lp_Kalman->Zk[0] = 0.0 - g_Navi.Vn[0]; // 北向速度应为0
            lp_Kalman->Zk[1] = 0.0 - g_Navi.Vn[1]; // 天向速度应为0
            lp_Kalman->Zk[2] = 0.0 - g_Navi.Vn[2]; // 东向速度应为0
            break;
            
        default:
            break;
    }
}
