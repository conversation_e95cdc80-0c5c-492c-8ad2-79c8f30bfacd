#ifndef _HPM6750_FUNCTIONS_H_
#define _HPM6750_FUNCTIONS_H_

/*
 * HPM6750算法移植 - 函数声明文件
 * 移植自HPM6750项目的FUNCTION.h
 */

#include "hpm6750_datastruct.h"

#ifdef __cplusplus
extern "C" {
#endif

// 惯导计算函数声明 (来自navi.c)
void Navi_Init(p_InitBind const lp_InitBind, p_InertialSysAlign const lp_InertialSysAlign, p_Navi lp_Navi);
void NaviCompute(DPARA const Gyro[3], DPARA const LastGyro[3], DPARA const Acc[3], DPARA const LastAcc[3], p_Navi lp_Navi);

// 地球参数计算函数
void ComputeWien(LATI r_Lati, ANGRATE r_Wien[3]);
void ComputeRmRn(LATI r_Lati, HEIGHT Height, LEN *lp_Rm, LEN *lp_Rn, DPARA *lp_invRm, DPARA *lp_invRn);
void ComputeWenn(LATI r_Lati, VEL const Vn[3], DPARA invRm, DPARA invRn, ANGRATE r_Wenn[3]);
void ComputeG(LATI r_Lati, HEIGHT Height, ACCELER *lp_Gn);

// 姿态计算函数
void ComputeWnbb(ANGRATE r_Wibb[2][3], ANGRATE const r_Wien[3], ANGRATE const r_Wenn[3], MATR Cnb[9], ANGRATE r_Wnbb[2][3]);
void ComputeDelSenbb(ANGRATE r_Wnbb[2][3], DELANG r_DelSenbb_1[3], DELANG r_DelSenbb_2[3], DELANG r_DelSenbb[3]);
void ComputeQ(DELANG r_DelSenbb[3], QUAT Q[4]);
void QToCnb(QUAT const Q[4], MATR Cnb[9]);
void CnbToQ(MATR Cnb[9], QUAT Q[4]);
void AttiToCnb(ATTI const r_Atti[3], MATR Cnb[9]);
void CnbToAtti(MATR const Cnb[9], ATTI r_Atti[3]);
void ComputeAttiRate(ANGRATE r_Wnbb[2][3], ATTI const r_Atti[3], ANGRATE r_AttiRate[3]);

// 速度位置计算函数
void ComputeVibn(p_Navi lp_Navi);
void ComputeVn(p_Navi lp_Navi);
void ComputePos(VEL const LastVn[3], VEL const Vn[3], DPARA invRm, DPARA invRn, LATI *lp_Lati, LOGI *lp_Logi, HEIGHT *lp_Height, LEN Sn[3]);
void ComputeVb(VEL const Vn[3], MATR const Cnb[9], VEL Vb[3]);

// 角度转换函数
void ComputeDeg_Ex(p_Navi lp_Navi);
void TransHeading0to360(p_Navi lp_Navi);

// 初始对准函数声明 (来自align.c)
void InertialSysAlign_Init(p_InitBind const lp_InitBind, p_InertialSysAlign lp_InertialSysAlign);
void InertialSysAlignCompute(DPARA const Gyro[3], DPARA const LastGyro[3], DPARA const Acc[3], DPARA const LastAcc[3], p_InertialSysAlign lp_InertialSysAlign);
void ComputeCen(LATI r_Lati, LOGI r_Logi, MATR Cen[9]);
void ComputeCie(TIME AlignTime, MATR Cie[9]);
void ComputeVi(ACCELER Gn, MATR Cie[9], MATR Cen[9], VEL Vi[3]);

// Kalman滤波函数声明 (来自kalman.c)
void Kalman_Init(p_Kalman lp_Kalman);
void KalCompute(p_GNSSData lp_GNSSData, p_Kalman lp_Kalman);
void KalPredict(p_Kalman lp_Kalman);
void ComputeFn(p_Navi const lp_Navi, MATR Fn[DIM_STATE * DIM_STATE]);
void ComputeFk(MATR Fn[DIM_STATE * DIM_STATE], MATR Fk[DIM_STATE * DIM_STATE]);
void ComputeXkk_1(MATR const Fk[DIM_STATE * DIM_STATE], VEC const Xk[DIM_STATE], VEC Xkk_1[DIM_STATE]);
void ComputeKk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV]);
void ComputePk(MATR Pkk_1[DIM_STATE * DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], MATR Kk[DIM_STATE * DIM_MAX_OBV], MATR Pk[DIM_STATE * DIM_STATE]);
void ComputeZk(p_GNSSData const lp_GNSSData, p_Kalman lp_Kalman, MODE Obv_Fit_Mode);
void ComputeXk(VEC Xkk_1[DIM_STATE], MATR Hk[DIM_MAX_OBV * DIM_STATE], VEC const Zk[DIM_MAX_OBV], MATR Kk[DIM_STATE * DIM_MAX_OBV], VEC Xk[DIM_STATE]);

// 误差修正函数
void ErrCorrect(p_Navi lp_Navi, p_Kalman lp_Kalman, p_GNSSData lp_GNSSData);
void CorrectPos(LATI *lp_InsLati, LOGI *lp_InsLogi, HEIGHT *lp_InsHeight, VEC PosErr[3]);
void CorrectVn(VEC InsVn[3], VEC VnErr[3]);
void CorrectAtti(QUAT Q[4], VEC AttiErr[3]);
void CorrectGyroBias(ANGRATE GyroBias[3], ANGRATE EstGyroBias[3]);
void CorrectAccBias(ACCELER AccBias[3], ACCELER EstAccBias[3]);

// 矩阵向量运算函数声明 (来自matvecmath.c)
void Mat_Mul(MATR const A[], MATR const B[], MATR C[], IPARA m, IPARA n, IPARA p);
void Mat_Add(MATR const A[], MATR const B[], MATR C[], IPARA m, IPARA n);
void Mat_Sub(MATR const A[], MATR const B[], MATR C[], IPARA m, IPARA n);
void Mat_Tr(MATR const A[], MATR B[], IPARA m, IPARA n);
void Mat_Inv(MATR A[], MATR B[], IPARA n);
void Vec_Cross(VEC const A[3], VEC const B[3], VEC C[3]);
void Vec_Dot(VEC const A[], VEC const B[], VEC *C, IPARA n);
void Vec_Norm(VEC const A[], VEC *norm, IPARA n);

// 算法入口函数声明
void HPM6750_Algorithm_Init(void);
void HPM6750_Algorithm_Process(DPARA const Gyro[3], DPARA const Acc[3], p_GNSSData lp_GNSSData, p_Navi lp_NaviOut);

#ifdef __cplusplus
}
#endif

#endif // _HPM6750_FUNCTIONS_H_
