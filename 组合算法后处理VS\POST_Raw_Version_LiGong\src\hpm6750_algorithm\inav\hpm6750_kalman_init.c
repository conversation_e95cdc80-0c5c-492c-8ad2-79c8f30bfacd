/*****************************************************文件说明******************************************************************************/
/*文件名称：hpm6750_kalman_init.c - HPM6750卡尔曼滤波矩阵初始化函数                                                                         */
/*版本号：  Ver 1.0                                                                                                                        */
/*移植日期：2025-01-18                                                                                                                      */
/*移植说明：从HPM6750项目移植卡尔曼滤波相关的矩阵初始化函数                                                                                 */
/*******************************************************************************************************************************************/

#include "../include/hpm6750_types.h"
#include "../include/hpm6750_datastruct.h"
#include "../include/hpm6750_functions.h"

/*********************************函数说明***************************************/
/*函数名称：Pk_Init                                                              */
/*函数功能描述：初始化协方差矩阵Pk                                               */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入输出变量：协方差矩阵Pk                                                     */
/*返回值：无                                                                     */
/*********************************************************************************/
void Pk_Init(MATR Pk[DIM_STATE * DIM_STATE])
{
    IPARA i, j;
    
    // 初始化为零矩阵
    for (i = 0; i < DIM_STATE * DIM_STATE; i++)
    {
        Pk[i] = 0.0;
    }
    
    // 设置对角元素
    // 位置误差方差 (m^2)
    Pk[0 * DIM_STATE + 0] = 100.0;  // 北向位置误差
    Pk[1 * DIM_STATE + 1] = 100.0;  // 天向位置误差
    Pk[2 * DIM_STATE + 2] = 100.0;  // 东向位置误差
    
    // 速度误差方差 ((m/s)^2)
    Pk[3 * DIM_STATE + 3] = 1.0;    // 北向速度误差
    Pk[4 * DIM_STATE + 4] = 1.0;    // 天向速度误差
    Pk[5 * DIM_STATE + 5] = 1.0;    // 东向速度误差
    
    // 姿态误差方差 (rad^2)
    Pk[6 * DIM_STATE + 6] = (1.0 * D2R) * (1.0 * D2R);  // 俯仰角误差
    Pk[7 * DIM_STATE + 7] = (1.0 * D2R) * (1.0 * D2R);  // 横滚角误差
    Pk[8 * DIM_STATE + 8] = (5.0 * D2R) * (5.0 * D2R);  // 航向角误差
    
    // 陀螺偏差方差 ((rad/s)^2)
    Pk[9 * DIM_STATE + 9] = (0.1 * D2R / 3600.0) * (0.1 * D2R / 3600.0);   // X轴陀螺偏差
    Pk[10 * DIM_STATE + 10] = (0.1 * D2R / 3600.0) * (0.1 * D2R / 3600.0); // Y轴陀螺偏差
    Pk[11 * DIM_STATE + 11] = (0.1 * D2R / 3600.0) * (0.1 * D2R / 3600.0); // Z轴陀螺偏差
    
    // 加速度计偏差方差 ((m/s^2)^2)
    Pk[12 * DIM_STATE + 12] = (0.001 * G0) * (0.001 * G0); // X轴加速度计偏差
    Pk[13 * DIM_STATE + 13] = (0.001 * G0) * (0.001 * G0); // Y轴加速度计偏差
    Pk[14 * DIM_STATE + 14] = (0.001 * G0) * (0.001 * G0); // Z轴加速度计偏差
}

/*********************************函数说明***************************************/
/*函数名称：Qk_Init                                                              */
/*函数功能描述：初始化过程噪声矩阵Qk                                             */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入输出变量：过程噪声矩阵Qk                                                   */
/*返回值：无                                                                     */
/*********************************************************************************/
void Qk_Init(MATR Qk[DIM_STATE * DIM_STATE])
{
    IPARA i;
    
    // 初始化为零矩阵
    for (i = 0; i < DIM_STATE * DIM_STATE; i++)
    {
        Qk[i] = 0.0;
    }
    
    // 设置对角元素 (根据系统噪声特性设置)
    // 位置过程噪声
    Qk[0 * DIM_STATE + 0] = 0.01;   // 北向位置噪声
    Qk[1 * DIM_STATE + 1] = 0.01;   // 天向位置噪声
    Qk[2 * DIM_STATE + 2] = 0.01;   // 东向位置噪声
    
    // 速度过程噪声
    Qk[3 * DIM_STATE + 3] = 0.01;   // 北向速度噪声
    Qk[4 * DIM_STATE + 4] = 0.01;   // 天向速度噪声
    Qk[5 * DIM_STATE + 5] = 0.01;   // 东向速度噪声
    
    // 姿态过程噪声
    Qk[6 * DIM_STATE + 6] = (0.1 * D2R) * (0.1 * D2R);  // 俯仰角噪声
    Qk[7 * DIM_STATE + 7] = (0.1 * D2R) * (0.1 * D2R);  // 横滚角噪声
    Qk[8 * DIM_STATE + 8] = (0.1 * D2R) * (0.1 * D2R);  // 航向角噪声
    
    // 陀螺偏差过程噪声
    Qk[9 * DIM_STATE + 9] = (0.01 * D2R / 3600.0) * (0.01 * D2R / 3600.0);   // X轴陀螺偏差噪声
    Qk[10 * DIM_STATE + 10] = (0.01 * D2R / 3600.0) * (0.01 * D2R / 3600.0); // Y轴陀螺偏差噪声
    Qk[11 * DIM_STATE + 11] = (0.01 * D2R / 3600.0) * (0.01 * D2R / 3600.0); // Z轴陀螺偏差噪声
    
    // 加速度计偏差过程噪声
    Qk[12 * DIM_STATE + 12] = (0.0001 * G0) * (0.0001 * G0); // X轴加速度计偏差噪声
    Qk[13 * DIM_STATE + 13] = (0.0001 * G0) * (0.0001 * G0); // Y轴加速度计偏差噪声
    Qk[14 * DIM_STATE + 14] = (0.0001 * G0) * (0.0001 * G0); // Z轴加速度计偏差噪声
}

/*********************************函数说明***************************************/
/*函数名称：Rk_Init                                                              */
/*函数功能描述：初始化观测噪声矩阵Rk                                             */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：观测拟合模式Obv_Fit_Mode                                             */
/*输入输出变量：观测噪声矩阵Rk                                                   */
/*返回值：无                                                                     */
/*********************************************************************************/
void Rk_Init(MATR Rk[DIM_MAX_OBV * DIM_MAX_OBV], MODE Obv_Fit_Mode)
{
    IPARA i;
    
    // 初始化为零矩阵
    for (i = 0; i < DIM_MAX_OBV * DIM_MAX_OBV; i++)
    {
        Rk[i] = 0.0;
    }
    
    switch(Obv_Fit_Mode)
    {
        case 1: // POSVEL_FIT - 位置速度观测
            // 位置观测噪声
            Rk[0 * DIM_MAX_OBV + 0] = 25.0;     // 纬度观测噪声 (m^2)
            Rk[1 * DIM_MAX_OBV + 1] = 25.0;     // 经度观测噪声 (m^2)
            Rk[2 * DIM_MAX_OBV + 2] = 100.0;    // 高度观测噪声 (m^2)
            // 速度观测噪声
            Rk[3 * DIM_MAX_OBV + 3] = 0.25;     // 北向速度观测噪声 ((m/s)^2)
            Rk[4 * DIM_MAX_OBV + 4] = 0.25;     // 天向速度观测噪声 ((m/s)^2)
            Rk[5 * DIM_MAX_OBV + 5] = 0.25;     // 东向速度观测噪声 ((m/s)^2)
            break;
            
        case 2: // POSVELHEAD_FIT - 位置速度航向观测
            // 位置观测噪声
            Rk[0 * DIM_MAX_OBV + 0] = 25.0;     // 纬度观测噪声 (m^2)
            Rk[1 * DIM_MAX_OBV + 1] = 25.0;     // 经度观测噪声 (m^2)
            Rk[2 * DIM_MAX_OBV + 2] = 100.0;    // 高度观测噪声 (m^2)
            // 速度观测噪声
            Rk[3 * DIM_MAX_OBV + 3] = 0.25;     // 北向速度观测噪声 ((m/s)^2)
            Rk[4 * DIM_MAX_OBV + 4] = 0.25;     // 天向速度观测噪声 ((m/s)^2)
            Rk[5 * DIM_MAX_OBV + 5] = 0.25;     // 东向速度观测噪声 ((m/s)^2)
            // 航向观测噪声
            Rk[6 * DIM_MAX_OBV + 6] = (1.0 * D2R) * (1.0 * D2R); // 航向观测噪声 (rad^2)
            break;
            
        case 3: // ZERO_SPEED - 零速观测
            // 速度观测噪声
            Rk[0 * DIM_MAX_OBV + 0] = 0.01;     // 北向速度观测噪声 ((m/s)^2)
            Rk[1 * DIM_MAX_OBV + 1] = 0.01;     // 天向速度观测噪声 ((m/s)^2)
            Rk[2 * DIM_MAX_OBV + 2] = 0.01;     // 东向速度观测噪声 ((m/s)^2)
            break;
            
        default:
            break;
    }
}

/*********************************函数说明***************************************/
/*函数名称：Hk_Init                                                              */
/*函数功能描述：初始化观测矩阵Hk                                                 */
/*版本号：  Ver 1.0                                                              */
/*移植日期：2025-01-18                                                            */
/*输入变量：观测拟合模式Obv_Fit_Mode                                             */
/*输入输出变量：观测矩阵Hk                                                       */
/*返回值：无                                                                     */
/*********************************************************************************/
void Hk_Init(MATR Hk[DIM_MAX_OBV * DIM_STATE], MODE Obv_Fit_Mode)
{
    IPARA i;
    
    // 初始化为零矩阵
    for (i = 0; i < DIM_MAX_OBV * DIM_STATE; i++)
    {
        Hk[i] = 0.0;
    }
    
    switch(Obv_Fit_Mode)
    {
        case 1: // POSVEL_FIT - 位置速度观测
            // 位置观测矩阵
            Hk[0 * DIM_STATE + 0] = 1.0;  // 北向位置
            Hk[1 * DIM_STATE + 1] = 1.0;  // 天向位置
            Hk[2 * DIM_STATE + 2] = 1.0;  // 东向位置
            // 速度观测矩阵
            Hk[3 * DIM_STATE + 3] = 1.0;  // 北向速度
            Hk[4 * DIM_STATE + 4] = 1.0;  // 天向速度
            Hk[5 * DIM_STATE + 5] = 1.0;  // 东向速度
            break;
            
        case 2: // POSVELHEAD_FIT - 位置速度航向观测
            // 位置观测矩阵
            Hk[0 * DIM_STATE + 0] = 1.0;  // 北向位置
            Hk[1 * DIM_STATE + 1] = 1.0;  // 天向位置
            Hk[2 * DIM_STATE + 2] = 1.0;  // 东向位置
            // 速度观测矩阵
            Hk[3 * DIM_STATE + 3] = 1.0;  // 北向速度
            Hk[4 * DIM_STATE + 4] = 1.0;  // 天向速度
            Hk[5 * DIM_STATE + 5] = 1.0;  // 东向速度
            // 航向观测矩阵
            Hk[6 * DIM_STATE + 8] = 1.0;  // 航向角
            break;
            
        case 3: // ZERO_SPEED - 零速观测
            // 速度观测矩阵
            Hk[0 * DIM_STATE + 3] = 1.0;  // 北向速度
            Hk[1 * DIM_STATE + 4] = 1.0;  // 天向速度
            Hk[2 * DIM_STATE + 5] = 1.0;  // 东向速度
            break;
            
        default:
            break;
    }
}
